// Image Generation Utility using Hugging Face
// Handles AI image generation from text prompts

/**
 * Available Hugging Face models for image generation
 */
const IMAGE_MODELS = {
  'stable-diffusion': 'stabilityai/stable-diffusion-2-1',
  'stable-diffusion-xl': 'stabilityai/stable-diffusion-xl-base-1.0',
  'dalle-mini': 'dalle-mini/dalle-mini',
  'openjourney': 'prompthero/openjourney'
};

/**
 * Default model to use
 */
const DEFAULT_MODEL = IMAGE_MODELS['stable-diffusion'];

/**
 * Detect if user message is requesting image generation
 * @param {string} message - User's message
 * @returns {boolean} - Whether image generation is requested
 */
function detectImageGenerationIntent(message) {
  if (!message || typeof message !== 'string') {
    return false;
  }

  const generationKeywords = [
    // Direct generation requests
    'generate image', 'create image', 'make image', 'draw image',
    'generate picture', 'create picture', 'make picture', 'draw picture',
    
    // Art requests
    'generate art', 'create art', 'make art', 'draw art',
    'paint', 'sketch', 'illustrate',
    
    // Specific commands
    'image of', 'picture of', 'drawing of', 'artwork of',
    'show me', 'visualize', 'render',
    
    // Creative requests
    'design', 'concept art', 'digital art'
  ];

  const lowerMessage = message.toLowerCase();
  
  // Check for generation keywords
  const hasGenerationKeywords = generationKeywords.some(keyword => 
    lowerMessage.includes(keyword)
  );

  // Check for command patterns
  const commandPatterns = [
    /(?:generate|create|make|draw|paint|sketch)\s+(?:an?|some)?\s*(?:image|picture|art|drawing)/i,
    /(?:image|picture|art|drawing)\s+of\s+/i,
    /show\s+me\s+(?:an?|some)?\s*(?:image|picture)/i
  ];

  const hasCommandPattern = commandPatterns.some(pattern => 
    pattern.test(message)
  );

  return hasGenerationKeywords || hasCommandPattern;
}

/**
 * Extract prompt from user message
 * @param {string} message - User's message
 * @returns {string} - Cleaned prompt for image generation
 */
function extractPrompt(message) {
  if (!message || typeof message !== 'string') {
    return 'a beautiful landscape';
  }

  let prompt = message.toLowerCase();

  // Remove common prefixes
  const prefixesToRemove = [
    'generate image of',
    'create image of',
    'make image of',
    'draw image of',
    'generate picture of',
    'create picture of',
    'make picture of',
    'draw picture of',
    'generate art of',
    'create art of',
    'make art of',
    'draw art of',
    'image of',
    'picture of',
    'drawing of',
    'artwork of',
    'show me',
    'visualize',
    'render',
    'paint',
    'sketch',
    'illustrate',
    'design'
  ];

  // Remove prefixes
  for (const prefix of prefixesToRemove) {
    if (prompt.startsWith(prefix)) {
      prompt = prompt.substring(prefix.length).trim();
      break;
    }
  }

  // Remove articles at the beginning
  prompt = prompt.replace(/^(a|an|the)\s+/i, '');

  // Clean up
  prompt = prompt.trim();

  // Fallback if prompt is empty
  if (!prompt || prompt.length < 3) {
    return 'a beautiful landscape';
  }

  return prompt;
}

/**
 * Generate image using Hugging Face API
 * @param {string} prompt - Text prompt for image generation
 * @param {string} model - Model to use (optional)
 * @returns {Promise<Object>} - Generated image data
 */
async function generateImage(prompt, model = DEFAULT_MODEL) {
  try {
    console.log(`🎨 Generating image with prompt: "${prompt}"`);
    console.log(`🤖 Using model: ${model}`);

    const response = await fetch(
      `https://api-inference.huggingface.co/models/${model}`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          inputs: prompt,
          parameters: {
            num_inference_steps: 20,
            guidance_scale: 7.5,
            width: 512,
            height: 512
          }
        })
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Hugging Face API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    // Get image as buffer
    const imageBuffer = await response.arrayBuffer();
    
    if (!imageBuffer || imageBuffer.byteLength === 0) {
      throw new Error('Received empty image data');
    }

    console.log(`✅ Image generated successfully (${imageBuffer.byteLength} bytes)`);

    return {
      success: true,
      imageBuffer: Buffer.from(imageBuffer),
      prompt: prompt,
      model: model,
      size: imageBuffer.byteLength
    };

  } catch (error) {
    console.error('❌ Image generation error:', error);
    return {
      success: false,
      error: error.message,
      prompt: prompt
    };
  }
}

/**
 * Generate image and format for Discord
 * @param {string} userMessage - User's message
 * @returns {Promise<Object>} - Formatted image result
 */
async function generateAndFormat(userMessage) {
  try {
    // Extract prompt from user message
    const prompt = extractPrompt(userMessage);
    
    // Generate image
    const result = await generateImage(prompt);
    
    if (result.success) {
      return {
        success: true,
        prompt: prompt,
        imageBuffer: result.imageBuffer,
        filename: `generated_${Date.now()}.png`,
        size: result.size
      };
    } else {
      return {
        success: false,
        prompt: prompt,
        error: result.error
      };
    }
    
  } catch (error) {
    console.error('❌ Image generation and formatting error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  detectImageGenerationIntent,
  extractPrompt,
  generateImage,
  generateAndFormat,
  IMAGE_MODELS,
  DEFAULT_MODEL
};
