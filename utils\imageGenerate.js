// Image Generation Utility using Hugging Face
// Handles AI image generation from text prompts

/**
 * Available models for image generation
 * Note: Many HF Inference API models have been moved to paid tiers
 */
const IMAGE_MODELS = {
  // Try these models in order of preference
  'dalle-mini': 'dalle-mini/dalle-mini',
  'openjourney': 'prompthero/openjourney',
  'stable-diffusion': 'runwayml/stable-diffusion-v1-5',
  'pollinations': 'pollinations' // Fallback to Pollinations API
};

/**
 * Default model to use
 */
const DEFAULT_MODEL = IMAGE_MODELS['dalle-mini'];

/**
 * Detect if user message is requesting image generation
 * @param {string} message - User's message
 * @returns {boolean} - Whether image generation is requested
 */
function detectImageGenerationIntent(message) {
  if (!message || typeof message !== 'string') {
    return false;
  }

  const generationKeywords = [
    // Direct generation requests
    'generate image', 'create image', 'make image', 'draw image',
    'generate picture', 'create picture', 'make picture', 'draw picture',
    
    // Art requests
    'generate art', 'create art', 'make art', 'draw art',
    'paint', 'sketch', 'illustrate',
    
    // Specific commands
    'image of', 'picture of', 'drawing of', 'artwork of',
    'show me', 'visualize', 'render',
    
    // Creative requests
    'design', 'concept art', 'digital art'
  ];

  const lowerMessage = message.toLowerCase();
  
  // Check for generation keywords
  const hasGenerationKeywords = generationKeywords.some(keyword => 
    lowerMessage.includes(keyword)
  );

  // Check for command patterns
  const commandPatterns = [
    /(?:generate|create|make|draw|paint|sketch)\s+(?:an?|some)?\s*(?:image|picture|art|drawing)/i,
    /(?:image|picture|art|drawing)\s+of\s+/i,
    /show\s+me\s+(?:an?|some)?\s*(?:image|picture)/i
  ];

  const hasCommandPattern = commandPatterns.some(pattern => 
    pattern.test(message)
  );

  return hasGenerationKeywords || hasCommandPattern;
}

/**
 * Extract prompt from user message
 * @param {string} message - User's message
 * @returns {string} - Cleaned prompt for image generation
 */
function extractPrompt(message) {
  if (!message || typeof message !== 'string') {
    return 'a beautiful landscape';
  }

  let prompt = message.toLowerCase();

  // Remove common prefixes
  const prefixesToRemove = [
    'generate an image of',
    'generate image of',
    'create an image of',
    'create image of',
    'make an image of',
    'make image of',
    'draw an image of',
    'draw image of',
    'generate a picture of',
    'generate picture of',
    'create a picture of',
    'create picture of',
    'make a picture of',
    'make picture of',
    'draw a picture of',
    'draw picture of',
    'generate art of',
    'create art of',
    'make art of',
    'draw art of',
    'an image of',
    'a picture of',
    'image of',
    'picture of',
    'drawing of',
    'artwork of',
    'show me',
    'visualize',
    'render',
    'paint',
    'sketch',
    'illustrate',
    'design'
  ];

  // Remove prefixes
  for (const prefix of prefixesToRemove) {
    if (prompt.startsWith(prefix)) {
      prompt = prompt.substring(prefix.length).trim();
      break;
    }
  }

  // Remove articles at the beginning
  prompt = prompt.replace(/^(a|an|the)\s+/i, '');

  // Clean up
  prompt = prompt.trim();

  // Fallback if prompt is empty
  if (!prompt || prompt.length < 3) {
    return 'a beautiful landscape';
  }

  return prompt;
}

/**
 * Generate image using Hugging Face API
 * @param {string} prompt - Text prompt for image generation
 * @param {string} model - Model to use (optional)
 * @returns {Promise<Object>} - Generated image data
 */
/**
 * Generate image using Pollinations API (free, no API key required)
 * @param {string} prompt - Text prompt for image generation
 * @returns {Promise<Object>} - Generated image data
 */
async function generateImageWithPollinations(prompt) {
  try {
    console.log(`🌸 Using Pollinations API for: "${prompt}"`);

    // Pollinations API - completely free, no API key needed
    const encodedPrompt = encodeURIComponent(prompt);
    const imageUrl = `https://image.pollinations.ai/prompt/${encodedPrompt}?width=512&height=512&nologo=true`;

    console.log(`🔗 Fetching from: ${imageUrl}`);

    const response = await fetch(imageUrl);

    if (!response.ok) {
      throw new Error(`Pollinations API error: ${response.status} ${response.statusText}`);
    }

    const imageBuffer = await response.arrayBuffer();

    if (!imageBuffer || imageBuffer.byteLength === 0) {
      throw new Error('Received empty image data from Pollinations');
    }

    console.log(`✅ Image generated with Pollinations (${imageBuffer.byteLength} bytes)`);

    return {
      success: true,
      imageBuffer: Buffer.from(imageBuffer),
      prompt: prompt,
      model: 'pollinations',
      size: imageBuffer.byteLength
    };

  } catch (error) {
    console.error('❌ Pollinations error:', error);
    return {
      success: false,
      error: error.message,
      prompt: prompt
    };
  }
}

async function generateImage(prompt, model = DEFAULT_MODEL) {
  try {
    console.log(`🎨 Generating image with prompt: "${prompt}"`);
    console.log(`🤖 Using model: ${model}`);

    // If model is pollinations or HF API key is missing, use Pollinations
    if (model === 'pollinations' || !process.env.HUGGINGFACE_API_KEY) {
      console.log(`🌸 Falling back to Pollinations API (free, no API key required)`);
      return await generateImageWithPollinations(prompt);
    }

    // Try Hugging Face API first
    const response = await fetch(
      'https://api-inference.huggingface.co/models/' + model,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          inputs: prompt,
          parameters: {
            guidance_scale: 7.5,
            num_inference_steps: 20,
            width: 512,
            height: 512
          },
          options: {
            wait_for_model: true,
            use_cache: false
          }
        })
      }
    );

    console.log(`🔍 HF API Response: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ HF Error details: ${errorText}`);

      // Fallback to Pollinations if HF fails
      console.log(`🔄 HF API failed, falling back to Pollinations...`);
      return await generateImageWithPollinations(prompt);
    }

    // Check if response is JSON (error) or binary (image)
    const contentType = response.headers.get('content-type');
    console.log(`📋 Content type: ${contentType}`);

    if (contentType && contentType.includes('application/json')) {
      const jsonResponse = await response.json();
      console.log(`❌ JSON response (likely error):`, jsonResponse);
      // Fallback to Pollinations
      console.log(`🔄 HF returned JSON error, falling back to Pollinations...`);
      return await generateImageWithPollinations(prompt);
    }

    // Get image as buffer
    const imageBuffer = await response.arrayBuffer();

    if (!imageBuffer || imageBuffer.byteLength === 0) {
      console.log(`🔄 Empty response from HF, falling back to Pollinations...`);
      return await generateImageWithPollinations(prompt);
    }

    console.log(`✅ Image generated with HF (${imageBuffer.byteLength} bytes)`);

    return {
      success: true,
      imageBuffer: Buffer.from(imageBuffer),
      prompt: prompt,
      model: model,
      size: imageBuffer.byteLength
    };

  } catch (error) {
    console.error('❌ HF Image generation error:', error);
    // Final fallback to Pollinations
    console.log(`🔄 HF completely failed, trying Pollinations as last resort...`);
    return await generateImageWithPollinations(prompt);
  }
}

/**
 * Test API connection
 * @returns {Promise<boolean>} - Whether API is accessible
 */
async function testAPI() {
  try {
    if (!process.env.HUGGINGFACE_API_KEY) {
      console.log('❌ HUGGINGFACE_API_KEY not found');
      return false;
    }

    console.log('🔍 Testing Hugging Face API connection...');

    // Test with a simple model first
    const response = await fetch(
      'https://api-inference.huggingface.co/models/' + DEFAULT_MODEL,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          inputs: 'test image',
          options: { wait_for_model: false }
        })
      }
    );

    console.log(`🔍 API Test Response: ${response.status} ${response.statusText}`);

    // 401/403 = auth issues, 404 = model not found, 503 = model loading
    // We consider 503 as "working" since it means the API is accessible
    return response.status !== 401 && response.status !== 403;

  } catch (error) {
    console.log(`❌ API Test Error: ${error.message}`);
    return false;
  }
}

/**
 * Generate image and format for Discord
 * @param {string} userMessage - User's message
 * @returns {Promise<Object>} - Formatted image result
 */
async function generateAndFormat(userMessage) {
  try {
    // Extract prompt from user message
    const prompt = extractPrompt(userMessage);
    console.log(`🔍 Extracted prompt: "${prompt}"`);

    // Generate image
    const result = await generateImage(prompt);

    if (result.success) {
      return {
        success: true,
        prompt: prompt,
        imageBuffer: result.imageBuffer,
        filename: `generated_${Date.now()}.png`,
        size: result.size
      };
    } else {
      return {
        success: false,
        prompt: prompt,
        error: result.error
      };
    }

  } catch (error) {
    console.error('❌ Image generation and formatting error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  detectImageGenerationIntent,
  extractPrompt,
  generateImage,
  generateAndFormat,
  testAPI,
  IMAGE_MODELS,
  DEFAULT_MODEL
};
