const memory = require('./memory.js');
const search = require('./search.js');
const imageRead = require('./imageRead.js');
const imageGenerate = require('./imageGenerate.js');

/**
 * Generate AI response with image analysis
 * @param {string} userMessage - The user's message
 * @param {Object} model - The Gemini AI model instance
 * @param {string} systemPrompt - The system prompt with rules
 * @param {string} userId - Discord user ID for memory context
 * @param {Array} imageAttachments - Array of Discord image attachments
 * @returns {Promise<string>} - The AI response
 */
async function generateAIResponseWithImages(userMessage, model, systemPrompt, userId, imageAttachments) {
  try {
    // Clean user message (remove mentions)
    const cleanedUserMessage = cleanUserMessage(userMessage);

    // Get memory context for this user
    const memoryContext = memory.formatMemoryForAI(userId);

    // Process images if any
    let imageContext = '';
    const imageParts = [];

    if (imageAttachments && imageAttachments.length > 0) {
      console.log(`🖼️ Processing ${imageAttachments.length} image(s)`);

      for (const attachment of imageAttachments) {
        // Validate image
        const validation = imageRead.validateImage(attachment);
        if (!validation.valid) {
          console.log(`❌ Invalid image: ${validation.errors.join(', ')}`);
          continue;
        }

        try {
          // Download and convert image
          const imageData = await imageRead.downloadImageAsBase64(attachment.url);
          const imageInfo = imageRead.getImageInfo(attachment);

          // Add image to Gemini parts
          imageParts.push({
            inlineData: {
              data: imageData.base64,
              mimeType: imageData.mimeType
            }
          });

          // Add image context for AI
          imageContext += `\nImage attached: ${imageInfo.name} (${imageInfo.extension}, ${(imageInfo.size / 1024).toFixed(1)}KB)`;

        } catch (error) {
          console.error(`❌ Error processing image ${attachment.name}:`, error);
          imageContext += `\nNote: Could not process image ${attachment.name || 'unknown'}`;
        }
      }
    }

    // Check if search is needed and perform search
    let searchContext = '';
    const needsSearch = search.detectSearchIntent(cleanedUserMessage);

    if (needsSearch) {
      console.log(`🔍 Search detected for: "${cleanedUserMessage}"`);
      const searchResults = await search.searchAndFormat(cleanedUserMessage);

      if (searchResults.success) {
        searchContext = searchResults.context;
        console.log(`✅ Search completed successfully`);
      } else {
        console.log(`❌ Search failed: ${searchResults.error}`);
      }
    }

    // Create the full prompt with all contexts
    const textPrompt = `${systemPrompt}${memoryContext}${searchContext}${imageContext}\n\nCurrent user message: ${cleanedUserMessage}`;

    // Prepare content for Gemini
    const content = [{ text: textPrompt }, ...imageParts];

    // Generate response from AI
    const result = await model.generateContent(content);
    const aiResponse = result.response.text();

    // Clean and validate response
    const cleanedResponse = cleanResponse(aiResponse);

    // Store this conversation in memory
    memory.addToMemory(userId, cleanedUserMessage, cleanedResponse);

    return cleanedResponse;

  } catch (error) {
    console.error('AI Response Error:', error);
    throw new Error('Failed to generate AI response');
  }
}

/**
 * Generate AI response using Gemini model with memory context (text only)
 * @param {string} userMessage - The user's message
 * @param {Object} model - The Gemini AI model instance
 * @param {string} systemPrompt - The system prompt with rules
 * @param {string} userId - Discord user ID for memory context
 * @returns {Promise<string>} - The AI response
 */
async function generateAIResponse(userMessage, model, systemPrompt, userId) {
  try {
    // Clean user message (remove mentions)
    const cleanedUserMessage = cleanUserMessage(userMessage);

    // Get memory context for this user
    const memoryContext = memory.formatMemoryForAI(userId);

    // Check if search is needed and perform search
    let searchContext = '';
    const needsSearch = search.detectSearchIntent(cleanedUserMessage);

    if (needsSearch) {
      console.log(`🔍 Search detected for: "${cleanedUserMessage}"`);
      const searchResults = await search.searchAndFormat(cleanedUserMessage);

      if (searchResults.success) {
        searchContext = searchResults.context;
        console.log(`✅ Search completed successfully`);
      } else {
        console.log(`❌ Search failed: ${searchResults.error}`);
      }
    }

    // Create the full prompt with system prompt, memory context, search context, and current message
    const fullPrompt = `${systemPrompt}${memoryContext}${searchContext}\n\nCurrent user message: ${cleanedUserMessage}`;

    // Generate response from AI
    const result = await model.generateContent(fullPrompt);
    const aiResponse = result.response.text();

    // Clean and validate response
    const cleanedResponse = cleanResponse(aiResponse);

    // Store this conversation in memory
    memory.addToMemory(userId, cleanedUserMessage, cleanedResponse);

    return cleanedResponse;

  } catch (error) {
    console.error('AI Response Error:', error);
    throw new Error('Failed to generate AI response');
  }
}

/**
 * Clean user message (remove mentions and extra whitespace)
 * @param {string} message - Raw user message
 * @returns {string} - Cleaned message
 */
function cleanUserMessage(message) {
  if (!message || typeof message !== 'string') {
    return '';
  }

  // Remove mentions (like <@123456789>)
  let cleaned = message.replace(/<@!?\d+>/g, '').trim();

  // Remove extra whitespace
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  return cleaned || 'hi';
}

/**
 * Clean and validate AI response
 * @param {string} response - Raw AI response
 * @returns {string} - Cleaned response
 */
function cleanResponse(response) {
  if (!response || typeof response !== 'string') {
    return 'Sorry, I couldn\'t generate a proper response.';
  }

  // Trim whitespace
  let cleaned = response.trim();

  // Ensure response is not too long for Discord (2000 char limit)
  if (cleaned.length > 1900) {
    cleaned = cleaned.substring(0, 1900) + '...';
  }

  // Remove any potential harmful content markers
  cleaned = cleaned.replace(/\[HARMFUL\]|\[NSFW\]|\[INAPPROPRIATE\]/gi, '');

  return cleaned || 'Sorry, I couldn\'t generate a response.';
}

/**
 * Build system prompt from config
 * @param {Object} config - Configuration object with prompt and rules
 * @returns {string} - Complete system prompt
 */
function buildSystemPrompt(config) {
  let systemPrompt = config.prompt || 'You are a helpful AI assistant.';

  if (config.rules && Array.isArray(config.rules)) {
    systemPrompt += '\n\nRules you must follow:\n';
    config.rules.forEach((rule, index) => {
      systemPrompt += `${index + 1}. ${rule}\n`;
    });
  }

  return systemPrompt;
}

/**
 * Check if user wants image generation and handle accordingly
 * @param {string} userMessage - The user's message
 * @param {Object} model - The Gemini AI model instance
 * @param {string} systemPrompt - The system prompt with rules
 * @param {string} userId - Discord user ID for memory context
 * @returns {Promise<Object>} - Response object with type and content
 */
async function handleUserRequest(userMessage, model, systemPrompt, userId) {
  try {
    // Clean user message (remove mentions)
    const cleanedUserMessage = cleanUserMessage(userMessage);

    // Check if user wants image generation
    const wantsImageGeneration = imageGenerate.detectImageGenerationIntent(cleanedUserMessage);

    if (wantsImageGeneration) {
      console.log(`🎨 Image generation request detected: "${cleanedUserMessage}"`);

      // Generate image
      const imageResult = await imageGenerate.generateAndFormat(cleanedUserMessage);

      if (imageResult.success) {
        // Store in memory
        memory.addToMemory(userId, cleanedUserMessage, `Generated image: ${imageResult.prompt}`);

        return {
          type: 'image',
          success: true,
          imageBuffer: imageResult.imageBuffer,
          filename: imageResult.filename,
          prompt: imageResult.prompt,
          textResponse: `🎨 Here's your generated image: **${imageResult.prompt}**`
        };
      } else {
        // Fallback to text response if image generation fails
        const textResponse = `Sorry, I couldn't generate an image for "${imageResult.prompt}". Error: ${imageResult.error}`;
        memory.addToMemory(userId, cleanedUserMessage, textResponse);

        return {
          type: 'text',
          success: false,
          textResponse: textResponse
        };
      }
    } else {
      // Handle as regular text conversation
      const response = await generateAIResponse(userMessage, model, systemPrompt, userId);

      return {
        type: 'text',
        success: true,
        textResponse: response
      };
    }

  } catch (error) {
    console.error('❌ Error handling user request:', error);
    return {
      type: 'text',
      success: false,
      textResponse: 'Sorry, I encountered an error processing your request.'
    };
  }
}

module.exports = {
  generateAIResponse,
  generateAIResponseWithImages,
  handleUserRequest,
  cleanResponse,
  cleanUserMessage,
  buildSystemPrompt
};