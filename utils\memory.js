// In-memory storage for user conversations
// Structure: { userId: [{ user: "message", bot: "response", timestamp: Date }] }
const userMemories = new Map();

// Maximum number of conversations to store per user
const MAX_CONVERSATIONS = 10;

/**
 * Add a conversation to user's memory
 * @param {string} userId - Discord user ID
 * @param {string} userMessage - User's message
 * @param {string} botResponse - <PERSON><PERSON>'s response
 */
function addToMemory(userId, userMessage, botResponse) {
  try {
    // Get existing memory or create new array
    let userConversations = userMemories.get(userId) || [];

    // Create conversation entry
    const conversation = {
      user: userMessage,
      bot: botResponse,
      timestamp: new Date()
    };

    // Add to memory
    userConversations.push(conversation);

    // Keep only the last MAX_CONVERSATIONS
    if (userConversations.length > MAX_CONVERSATIONS) {
      userConversations = userConversations.slice(-MAX_CONVERSATIONS);
    }

    // Update memory
    userMemories.set(userId, userConversations);

    console.log(`💾 Stored conversation for user ${userId} (${userConversations.length}/${MAX_CONVERSATIONS})`);

  } catch (error) {
    console.error('Memory storage error:', error);
  }
}

/**
 * Get user's conversation history
 * @param {string} userId - Discord user ID
 * @returns {Array} - Array of conversation objects
 */
function getMemory(userId) {
  return userMemories.get(userId) || [];
}

/**
 * Format memory for AI context
 * @param {string} userId - Discord user ID
 * @returns {string} - Formatted conversation history
 */
function formatMemoryForAI(userId) {
  const conversations = getMemory(userId);

  if (conversations.length === 0) {
    return '';
  }

  let memoryContext = '\n\nPrevious conversation history:\n';

  conversations.forEach((conv, index) => {
    memoryContext += `${index + 1}. User: ${conv.user}\n`;
    memoryContext += `   Bot: ${conv.bot}\n`;
  });

  memoryContext += '\nUse this context to provide more relevant and personalized responses.\n';

  return memoryContext;
}

/**
 * Clear memory for a specific user
 * @param {string} userId - Discord user ID
 */
function clearMemory(userId) {
  userMemories.delete(userId);
  console.log(`🗑️ Cleared memory for user ${userId}`);
}

/**
 * Get memory statistics
 * @returns {Object} - Memory usage statistics
 */
function getMemoryStats() {
  const totalUsers = userMemories.size;
  let totalConversations = 0;

  userMemories.forEach(conversations => {
    totalConversations += conversations.length;
  });

  return {
    totalUsers,
    totalConversations,
    averageConversationsPerUser: totalUsers > 0 ? (totalConversations / totalUsers).toFixed(2) : 0
  };
}

/**
 * Clean old memories (optional - for memory management)
 * Remove conversations older than specified days
 * @param {number} daysOld - Days to keep conversations
 */
function cleanOldMemories(daysOld = 7) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);

  let cleanedCount = 0;

  userMemories.forEach((conversations, userId) => {
    const filteredConversations = conversations.filter(conv => conv.timestamp > cutoffDate);

    if (filteredConversations.length !== conversations.length) {
      if (filteredConversations.length === 0) {
        userMemories.delete(userId);
      } else {
        userMemories.set(userId, filteredConversations);
      }
      cleanedCount++;
    }
  });

  console.log(`🧹 Cleaned old memories for ${cleanedCount} users`);
  return cleanedCount;
}

module.exports = {
  addToMemory,
  getMemory,
  formatMemoryForAI,
  clearMemory,
  getMemoryStats,
  cleanOldMemories
};