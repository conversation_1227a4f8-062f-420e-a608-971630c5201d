// Image Reading Utility
// Handles image processing and analysis for Discord attachments

/**
 * Supported image types and their MIME types
 */
const SUPPORTED_IMAGE_TYPES = {
  // Common formats
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/gif': ['.gif'],
  'image/webp': ['.webp'],
  'image/bmp': ['.bmp'],
  'image/tiff': ['.tiff', '.tif'],
  'image/svg+xml': ['.svg'],

  // Additional formats
  'image/x-icon': ['.ico'],
  'image/vnd.microsoft.icon': ['.ico'],
  'image/avif': ['.avif'],
  'image/heic': ['.heic'],
  'image/heif': ['.heif']
};

/**
 * Check if attachment is a supported image
 * @param {Object} attachment - Discord attachment object
 * @returns {boolean} - Whether the attachment is a supported image
 */
function isSupportedImage(attachment) {
  if (!attachment) {
    console.log('❌ No attachment provided');
    return false;
  }

  console.log(`🔍 Checking attachment:`, {
    name: attachment.name,
    contentType: attachment.contentType,
    url: attachment.url
  });

  // Check by content type first
  if (attachment.contentType) {
    const contentType = attachment.contentType.toLowerCase();
    const isSupported = Object.keys(SUPPORTED_IMAGE_TYPES).includes(contentType);
    console.log(`📋 Content type check: ${contentType} -> ${isSupported}`);
    if (isSupported) {
      return true;
    }
  }

  // Fallback to filename extension check
  if (attachment.name) {
    const extension = getFileExtension(attachment.name);
    const isSupported = Object.values(SUPPORTED_IMAGE_TYPES).some(extensions =>
      extensions.includes(extension)
    );
    console.log(`📁 Extension check: ${extension} -> ${isSupported}`);
    return isSupported;
  }

  // Last resort: check URL extension
  if (attachment.url) {
    const extension = getFileExtension(attachment.url);
    const isSupported = Object.values(SUPPORTED_IMAGE_TYPES).some(extensions =>
      extensions.includes(extension)
    );
    console.log(`🔗 URL extension check: ${extension} -> ${isSupported}`);
    return isSupported;
  }

  console.log('❌ No way to determine image type');
  return false;
}

/**
 * Get file extension from filename
 * @param {string} filename - The filename
 * @returns {string} - File extension with dot (e.g., '.jpg')
 */
function getFileExtension(filename) {
  if (!filename || typeof filename !== 'string') {
    return '';
  }

  const lastDot = filename.lastIndexOf('.');
  if (lastDot === -1) {
    return '';
  }

  return filename.substring(lastDot).toLowerCase();
}

/**
 * Download image and convert to base64
 * @param {string} imageUrl - URL of the image
 * @returns {Promise<string>} - Base64 encoded image data
 */
async function downloadImageAsBase64(imageUrl) {
  try {
    console.log(`📥 Downloading image: ${imageUrl}`);

    const response = await fetch(imageUrl);

    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
    }

    // Get the image data as array buffer
    const arrayBuffer = await response.arrayBuffer();

    // Convert to base64
    const base64 = Buffer.from(arrayBuffer).toString('base64');

    // Get content type from response or guess from URL
    let mimeType = response.headers.get('content-type');
    if (!mimeType) {
      mimeType = guessMimeTypeFromUrl(imageUrl);
    }

    console.log(`✅ Image downloaded successfully (${arrayBuffer.byteLength} bytes, ${mimeType})`);

    return {
      base64: base64,
      mimeType: mimeType,
      size: arrayBuffer.byteLength
    };

  } catch (error) {
    console.error('❌ Error downloading image:', error);
    throw new Error(`Failed to download image: ${error.message}`);
  }
}

/**
 * Guess MIME type from URL extension
 * @param {string} url - Image URL
 * @returns {string} - Guessed MIME type
 */
function guessMimeTypeFromUrl(url) {
  const extension = getFileExtension(url.split('?')[0]); // Remove query params

  for (const [mimeType, extensions] of Object.entries(SUPPORTED_IMAGE_TYPES)) {
    if (extensions.includes(extension)) {
      return mimeType;
    }
  }

  return 'image/jpeg'; // Default fallback
}

/**
 * Validate image size and type
 * @param {Object} attachment - Discord attachment object
 * @returns {Object} - Validation result
 */
function validateImage(attachment) {
  const MAX_SIZE = 20 * 1024 * 1024; // 20MB limit

  console.log(`🔍 Validating image:`, {
    name: attachment.name,
    contentType: attachment.contentType,
    size: attachment.size
  });

  const result = {
    valid: true,
    errors: []
  };

  // Check if it's a supported image type
  const isSupported = isSupportedImage(attachment);
  if (!isSupported) {
    result.valid = false;
    result.errors.push(`Unsupported image format (${attachment.contentType || 'unknown type'})`);
    console.log(`❌ Image validation failed: unsupported format`);
  } else {
    console.log(`✅ Image format is supported`);
  }

  // Check file size
  if (attachment.size && attachment.size > MAX_SIZE) {
    result.valid = false;
    result.errors.push(`Image too large (${(attachment.size / 1024 / 1024).toFixed(2)}MB). Maximum size is 20MB`);
    console.log(`❌ Image validation failed: too large`);
  }

  console.log(`🔍 Validation result:`, result);
  return result;
}

/**
 * Get image information
 * @param {Object} attachment - Discord attachment object
 * @returns {Object} - Image information
 */
function getImageInfo(attachment) {
  return {
    name: attachment.name || 'unknown',
    size: attachment.size || 0,
    contentType: attachment.contentType || guessMimeTypeFromUrl(attachment.url),
    url: attachment.url,
    width: attachment.width || null,
    height: attachment.height || null,
    extension: getFileExtension(attachment.name || attachment.url)
  };
}

module.exports = {
  isSupportedImage,
  downloadImageAsBase64,
  validateImage,
  getImageInfo,
  getFileExtension,
  guessMimeTypeFromUrl,
  SUPPORTED_IMAGE_TYPES
};