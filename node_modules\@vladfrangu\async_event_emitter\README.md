<div align="center">

# @vladfrangu/async_event_emitter

**Simple to use event emitter implementation with async support in mind.**

[![GitHub](https://img.shields.io/github/license/vladfrangu/async_event_emitter)](https://github.com/vladfrangu/async_event_emitter/blob/main/LICENSE.md)
[![codecov](https://codecov.io/gh/vladfrangu/async_event_emitter/branch/main/graph/badge.svg?token=0MSAyoZNxz)](https://codecov.io/gh/vladfrangu/async_event_emitter)
[![npm](https://img.shields.io/npm/v/@vladfrangu/async_event_emitter?color=crimson&logo=npm&style=flat-square)](https://www.npmjs.com/package/@vladfrangu/async_event_emitter)

</div>

## Description

A very small, lightweight and simple re-implementation of event emitters, with support for async event handlers in mind.

> **Note**: `@vladfrangu/async_event_emitter` requires Node.js v14 or higher to work, or a browser that has async/await support.

## Features

-   TypeScript friendly
-   Offers CJS, ESM and UMD builds
-   Consistent interface with what is expected from an event emitter
-   Simple handling of asynchronous event handlers to allow waiting for their execution to finish if you want to exit the process

## Buy me some doughnuts

Most of my projects are and always will be open source, even if I don't get donations. That being said, I know there are amazing people who may still want to donate just to show their appreciation. Thank you very much in advance!

I accept donations through Ko-fi, PayPal, Patreon and GitHub Sponsorships. You can use the buttons below to donate through your method of choice.

|   Donate With   |                         Address                          |
| :-------------: | :------------------------------------------------------: |
| GitHub Sponsors |   [Click Here](https://github.com/sponsors/vladfrangu)   |
|      Ko-fi      |       [Click Here](https://ko-fi.com/wolfgalvlad)        |
|     Patreon     |       [Click Here](https://patreon.com/vladfrangu)       |
|     PayPal      | [Click Here](https://www.paypal.com/paypalme/franguvlad) |

## Contributors ✨

Thanks goes to these wonderful people ([emoji key](https://allcontributors.org/docs/en/emoji-key)):

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<!-- markdownlint-enable -->
<!-- prettier-ignore-end -->

<!-- ALL-CONTRIBUTORS-LIST:END -->

This project follows the [all-contributors](https://github.com/all-contributors/all-contributors) specification. Contributions of any kind welcome!
