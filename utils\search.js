/**
 * Perform web search using Serper API
 * @param {string} query - Search query
 * @returns {Promise<Object>} - Search results object
 */
async function performSearch(query) {
  try {
    const response = await fetch('https://google.serper.dev/search', {
      method: 'POST',
      headers: {
        'X-API-KEY': process.env.SERPER_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        q: query,
        num: 5 // Get top 5 results
      })
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      success: true,
      query: query,
      results: data
    };

  } catch (error) {
    console.error('Search error:', error);
    return {
      success: false,
      query: query,
      error: error.message
    };
  }
}

/**
 * Detect if user message needs web search
 * @param {string} message - User's message
 * @returns {boolean} - Whether search is needed
 */
function detectSearchIntent(message) {
  if (!message || typeof message !== 'string') {
    return false;
  }

  const searchKeywords = [
    // Direct search requests
    'search for', 'look up', 'find information', 'google', 'search',

    // Current information requests
    'latest', 'recent', 'current', 'today', 'now', 'this year', 'new',

    // News and events
    'news', 'breaking', 'happened', 'event', 'update',

    // Weather and time-sensitive
    'weather', 'temperature', 'forecast', 'stock price', 'exchange rate',

    // Questions about current state
    'what is happening', 'what happened', 'tell me about', 'information about',

    // Specific domains that need current info
    'price of', 'cost of', 'how much', 'when did', 'who is', 'where is'
  ];

  const lowerMessage = message.toLowerCase();

  // Check for search keywords
  const hasSearchKeywords = searchKeywords.some(keyword =>
    lowerMessage.includes(keyword)
  );

  // Check for question patterns that might need current info
  const questionPatterns = [
    /what.*(?:latest|recent|current|new|today)/i,
    /when.*(?:did|will|does)/i,
    /who.*(?:is|was|are|were)/i,
    /where.*(?:is|are|can)/i,
    /how.*(?:much|many|to)/i,
    /why.*(?:did|is|are)/i
  ];

  const hasQuestionPattern = questionPatterns.some(pattern =>
    pattern.test(message)
  );

  return hasSearchKeywords || hasQuestionPattern;
}

/**
 * Format search results for AI context
 * @param {Object} searchData - Search results from Serper API
 * @returns {string} - Formatted search context
 */
function formatSearchResults(searchData) {
  if (!searchData.success || !searchData.results) {
    return '';
  }

  const { results } = searchData;
  let formattedResults = `\n\nCurrent web search results for "${searchData.query}":\n`;

  // Add organic search results
  if (results.organic && results.organic.length > 0) {
    formattedResults += '\nTop search results:\n';
    results.organic.slice(0, 3).forEach((result, index) => {
      formattedResults += `${index + 1}. ${result.title}\n`;
      formattedResults += `   ${result.snippet}\n`;
      formattedResults += `   Source: ${result.link}\n\n`;
    });
  }

  // Add knowledge graph if available
  if (results.knowledgeGraph) {
    const kg = results.knowledgeGraph;
    formattedResults += `Knowledge Graph:\n`;
    formattedResults += `Title: ${kg.title}\n`;
    if (kg.description) {
      formattedResults += `Description: ${kg.description}\n`;
    }
    if (kg.attributes) {
      Object.entries(kg.attributes).forEach(([key, value]) => {
        formattedResults += `${key}: ${value}\n`;
      });
    }
    formattedResults += '\n';
  }

  // Add people also ask if available
  if (results.peopleAlsoAsk && results.peopleAlsoAsk.length > 0) {
    formattedResults += 'Related questions:\n';
    results.peopleAlsoAsk.slice(0, 2).forEach((item, index) => {
      formattedResults += `${index + 1}. ${item.question}\n`;
    });
    formattedResults += '\n';
  }

  formattedResults += 'Use this current information to provide an accurate and up-to-date response.\n';

  return formattedResults;
}

/**
 * Perform search and format results (main function)
 * @param {string} query - Search query
 * @returns {Promise<Object>} - Formatted search results
 */
async function searchAndFormat(query) {
  const searchResults = await performSearch(query);

  if (searchResults.success) {
    const formattedContext = formatSearchResults(searchResults);
    return {
      success: true,
      query: query,
      context: formattedContext,
      rawResults: searchResults.results
    };
  } else {
    return {
      success: false,
      query: query,
      error: searchResults.error
    };
  }
}

module.exports = {
  performSearch,
  detectSearchIntent,
  formatSearchResults,
  searchAndFormat
};