// Simple Discord AI Chatbot
// Responds to mentions in any channel with AI responses

// Import packages
const { Client, GatewayIntentBits } = require('discord.js');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

// Bot ready event
client.once('ready', () => {
  console.log('Bot is online!');
});

// Message event handler
client.on('messageCreate', async (message) => {
  // Ignore bot messages
  if (message.author.bot) return;

  // Only respond when mentioned
  if (!message.mentions.has(client.user)) return;

  try {
    // Generate AI response
    const result = await model.generateContent(message.content);
    const aiResponse = result.response.text();

    // Send response
    await message.reply(aiResponse);

    console.log(`Responded to ${message.author.username}: "${message.content}"`);

  } catch (error) {
    console.error('Error:', error);
    await message.reply('Sorry, I had an error processing your message.');
  }
});

// Login to Discord
client.login(process.env.DISCORD_BOT_TOKEN);
