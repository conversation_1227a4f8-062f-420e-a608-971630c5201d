// Simple Discord AI Chatbot
// Responds to mentions in any channel with AI responses using config and utilities

// Import packages
const { Client, GatewayIntentBits } = require('discord.js');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');
const fs = require('fs');

// Import utilities
const aiResponse = require('./utils/aiResponse.js');

// Load environment variables
dotenv.config();

// Load configuration
const config = JSON.parse(fs.readFileSync('./config.json', 'utf8'));

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Build system prompt from config
const systemPrompt = aiResponse.buildSystemPrompt(config);

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

// Bot ready event
client.once('ready', () => {
  console.log('CybranceBot is online!');
  console.log(`System prompt loaded: ${systemPrompt.substring(0, 100)}...`);
});

// Message event handler
client.on('messageCreate', async (message) => {
  // Ignore bot messages
  if (message.author.bot) return;

  // Only respond when mentioned
  if (!message.mentions.has(client.user)) return;

  try {
    // Generate AI response using utility
    const response = await aiResponse.generateAIResponse(
      message.content,
      model,
      systemPrompt
    );

    // Send response
    await message.reply(response);

    console.log(`Responded to ${message.author.username}: "${message.content}"`);

  } catch (error) {
    console.error('Error:', error);
    await message.reply('Sorry, I had an error processing your message.');
  }
});

// Login to Discord
client.login(process.env.DISCORD_BOT_TOKEN);
