// Simple Discord AI Chatbot using Google Gemini AI
// Basic bot that responds to mentions with AI-generated responses

// Import packages
const { Client, GatewayIntentBits } = require('discord.js');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Simple system prompt
const systemPrompt = "You are a helpful Discord AI assistant. Be friendly, helpful, and keep responses under 2000 characters. Answer questions clearly and stay on topic.";

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

// Bot ready event
client.once('ready', () => {
  console.log('AI Bot is online!');
});

// Message event handler
client.on('messageCreate', async (message) => {
  // Ignore bot messages and only respond when mentioned
  if (message.author.bot || !message.mentions.has(client.user)) return;

  try {
    // Generate AI response
    const result = await model.generateContent([
      { text: systemPrompt },
      { text: `User message: ${message.content}` }
    ]);

    const aiResponse = result.response.text();

    // Reply to the message
    await message.reply(aiResponse);

    console.log(`Responded to ${message.author.username}: "${message.content}"`);

  } catch (error) {
    console.error('Error processing message:', error);
    await message.reply('Sorry, I encountered an error processing your message. Please try again.');
  }
});

// Login to Discord
client.login(process.env.DISCORD_BOT_TOKEN);
