// Import packages
const { Client, GatewayIntentBits } = require('discord.js');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');
const fs = require('fs');

// Import utilities
const aiResponse = require('./utils/aiResponse.js');

// Load environment variables
dotenv.config();

// Load configuration
const config = JSON.parse(fs.readFileSync('./config.json', 'utf8'));

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Build system prompt from config
const systemPrompt = aiResponse.buildSystemPrompt(config);

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

// Bot ready event
client.once('ready', () => {
  console.log('CybranceBot is online!');
  console.log(`System prompt loaded: ${systemPrompt.substring(0, 100)}...`);
});

// Track processed messages to prevent duplicates
const processedMessages = new Set();

// Message event handler
client.on('messageCreate', async (message) => {
  // Ignore bot messages
  if (message.author.bot) return;

  // Only respond when mentioned
  if (!message.mentions.has(client.user)) return;

  // Prevent duplicate processing
  if (processedMessages.has(message.id)) return;
  processedMessages.add(message.id);

  // Clean up old message IDs (keep only last 100)
  if (processedMessages.size > 100) {
    const oldIds = Array.from(processedMessages).slice(0, processedMessages.size - 100);
    oldIds.forEach(id => processedMessages.delete(id));
  }

  try {
    console.log(`Processing message from ${message.author.username}: "${message.content}"`);

    // Generate AI response using utility
    const response = await aiResponse.generateAIResponse(
      message.content,
      model,
      systemPrompt
    );

    // Send response
    await message.reply(response);

    console.log(`✅ Responded to ${message.author.username}`);

  } catch (error) {
    console.error('❌ Error:', error);
    await message.reply('Sorry, I had an error processing your message.');
  }
});

// Login to Discord
client.login(process.env.DISCORD_BOT_TOKEN);
