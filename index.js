// Import packages
const { Client, GatewayIntentBits } = require('discord.js');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');
const fs = require('fs');

// Import utilities
const aiResponse = require('./utils/aiResponse.js');
const imageRead = require('./utils/imageRead.js');
const imageGenerate = require('./utils/imageGenerate.js');

// Load environment variables
dotenv.config();

// Load configuration
const config = JSON.parse(fs.readFileSync('./config.json', 'utf8'));

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Build system prompt from config
const systemPrompt = aiResponse.buildSystemPrompt(config);

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
  ],
});

// Bot ready event
client.once('ready', async () => {
  console.log('CybranceBot is online!');
  console.log(`System prompt loaded: ${systemPrompt.substring(0, 100)}...`);

  // Test image generation API
  const apiWorking = await imageGenerate.testAPI();
  if (apiWorking) {
    console.log('✅ Hugging Face API connection successful');
  } else {
    console.log('❌ Hugging Face API connection failed - image generation may not work');
  }
});

// Track processed messages to prevent duplicates
const processedMessages = new Set();

// Message event handler
client.on('messageCreate', async (message) => {
  // Ignore bot messages
  if (message.author.bot) return;

  // Only respond when mentioned
  if (!message.mentions.has(client.user)) return;

  // Prevent duplicate processing
  if (processedMessages.has(message.id)) return;
  processedMessages.add(message.id);

  // Clean up old message IDs (keep only last 100)
  if (processedMessages.size > 100) {
    const oldIds = Array.from(processedMessages).slice(0, processedMessages.size - 100);
    oldIds.forEach(id => processedMessages.delete(id));
  }

  try {
    console.log(`Processing message from ${message.author.username}: "${message.content}"`);

    // Check for image attachments
    console.log(`📎 Total attachments: ${message.attachments.size}`);

    // Convert Discord Collection to Array first
    const allAttachments = Array.from(message.attachments.values());
    console.log(`📋 Converted to array: ${allAttachments.length} attachments`);

    const imageAttachments = allAttachments.filter(attachment => {
      console.log(`🔍 Checking attachment: ${attachment.name} (${attachment.contentType})`);
      const isSupported = imageRead.isSupportedImage(attachment);
      console.log(`📋 Is supported: ${isSupported}`);
      return isSupported;
    });

    console.log(`🖼️ Image attachments found: ${imageAttachments.length}`);

    let result;

    if (imageAttachments.length > 0) {
      // Handle message with images
      console.log(`📋 Processing ${imageAttachments.length} image attachments`);
      console.log(`📋 First attachment:`, {
        name: imageAttachments[0].name,
        contentType: imageAttachments[0].contentType,
        size: imageAttachments[0].size
      });

      const response = await aiResponse.generateAIResponseWithImages(
        message.content,
        model,
        systemPrompt,
        message.author.id,
        imageAttachments
      );

      // Send text response
      await message.reply(response);
      console.log(`✅ Responded to ${message.author.username} with image analysis`);

    } else {
      // Handle text-only message (could be image generation or regular chat)
      result = await aiResponse.handleUserRequest(
        message.content,
        model,
        systemPrompt,
        message.author.id
      );

      if (result.type === 'image' && result.success) {
        // Send generated image
        const { AttachmentBuilder } = require('discord.js');
        const attachment = new AttachmentBuilder(result.imageBuffer, {
          name: result.filename
        });

        await message.reply({
          content: result.textResponse,
          files: [attachment]
        });

        console.log(`✅ Responded to ${message.author.username} with generated image: "${result.prompt}"`);

      } else {
        // Send text response
        await message.reply(result.textResponse);
        console.log(`✅ Responded to ${message.author.username} with text`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
    await message.reply('Sorry, I had an error processing your message.');
  }
});

// Login to Discord
client.login(process.env.DISCORD_BOT_TOKEN);
