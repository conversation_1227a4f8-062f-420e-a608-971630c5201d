{"version": 3, "sources": ["../../src/lib/_AsyncQueueEntry.ts", "../../src/lib/AsyncQueue.ts"], "names": [], "mappings": ";;;;;;;;;EAKO,IAAM,gBAAA,GAAN,MAAM,gBAAgB,CAAA;EAAA,EAQrB,YAAY,KAAmB,EAAA;EAPtC,IAAgB,aAAA,CAAA,IAAA,EAAA,SAAA,CAAA;EAChB,IAAQ,aAAA,CAAA,IAAA,EAAA,SAAA,CAAA;EACR,IAAQ,aAAA,CAAA,IAAA,EAAA,QAAA,CAAA;EACR,IAAiB,aAAA,CAAA,IAAA,EAAA,OAAA,CAAA;EACjB,IAAA,aAAA,CAAA,IAAA,EAAQ,QAAqC,EAAA,IAAA,CAAA;EAC7C,IAAA,aAAA,CAAA,IAAA,EAAQ,gBAAsC,EAAA,IAAA,CAAA;EAG7C,IAAA,IAAA,CAAK,KAAQ,GAAA,KAAA;EACb,IAAA,IAAA,CAAK,OAAU,GAAA,IAAI,OAAQ,CAAA,CAAC,SAAS,MAAW,KAAA;EAC/C,MAAA,IAAA,CAAK,OAAU,GAAA,OAAA;EACf,MAAA,IAAA,CAAK,MAAS,GAAA,MAAA;EAAA,KACd,CAAA;EAAA;EACF,EAEO,UAAU,MAAqB,EAAA;EACrC,IAAI,IAAA,MAAA,CAAO,SAAgB,OAAA,IAAA;EAE3B,IAAA,IAAA,CAAK,MAAS,GAAA,MAAA;EACd,IAAA,IAAA,CAAK,iBAAiB,MAAM;EAC3B,MAAA,MAAM,QAAQ,IAAK,CAAA,KAAA,CAAM,UAAU,CAAA,CAAE,QAAQ,IAAI,CAAA;EACjD,MAAI,IAAA,KAAA,KAAU,IAAS,IAAA,CAAA,KAAA,CAAM,UAAU,CAAE,CAAA,MAAA,CAAO,OAAO,CAAC,CAAA;EAExD,MAAA,IAAA,CAAK,MAAO,CAAA,IAAI,KAAM,CAAA,0BAA0B,CAAC,CAAA;EAAA,KAClD;EACA,IAAA,IAAA,CAAK,MAAO,CAAA,gBAAA,CAAiB,OAAS,EAAA,IAAA,CAAK,cAAc,CAAA;EACzD,IAAO,OAAA,IAAA;EAAA;EACR,EAEO,GAAM,GAAA;EACZ,IAAA,IAAA,CAAK,OAAQ,EAAA;EACb,IAAA,IAAA,CAAK,OAAQ,EAAA;EACb,IAAO,OAAA,IAAA;EAAA;EACR,EAEO,KAAQ,GAAA;EACd,IAAA,IAAA,CAAK,OAAQ,EAAA;EACb,IAAA,IAAA,CAAK,MAAO,CAAA,IAAI,KAAM,CAAA,0BAA0B,CAAC,CAAA;EACjD,IAAO,OAAA,IAAA;EAAA;EACR,EAEQ,OAAU,GAAA;EACjB,IAAA,IAAI,KAAK,MAAQ,EAAA;EAChB,MAAA,IAAA,CAAK,MAAO,CAAA,mBAAA,CAAoB,OAAS,EAAA,IAAA,CAAK,cAAe,CAAA;EAC7D,MAAA,IAAA,CAAK,MAAS,GAAA,IAAA;EACd,MAAA,IAAA,CAAK,cAAiB,GAAA,IAAA;EAAA;EACvB;EAEF,CAAA;EAjD6B,MAAA,CAAA,gBAAA,EAAA,iBAAA,CAAA;EAAtB,IAAM,eAAN,GAAA,gBAAA;;;ECAA,IAAM,WAAA,GAAN,MAAM,WAAW,CAAA;EAAA,EAAjB,WAAA,GAAA;EAoBN;EAAA;EAAA;EAAA,IAAA,aAAA,CAAA,IAAA,EAAiB,YAA8B,EAAC,CAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA,EAfhD,IAAW,SAAoB,GAAA;EAC9B,IAAA,OAAO,KAAK,QAAS,CAAA,MAAA;EAAA;EACtB;EAAA;EAAA;EAAA;EAAA,EAMA,IAAW,MAAiB,GAAA;EAC3B,IAAA,OAAO,IAAK,CAAA,SAAA,KAAc,CAAI,GAAA,CAAA,GAAI,KAAK,SAAY,GAAA,CAAA;EAAA;EACpD;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA,EA4BO,KAAK,OAA0D,EAAA;EACrE,IAAM,MAAA,KAAA,GAAQ,IAAI,eAAA,CAAgB,IAAI,CAAA;EAEtC,IAAI,IAAA,IAAA,CAAK,QAAS,CAAA,MAAA,KAAW,CAAG,EAAA;EAC/B,MAAK,IAAA,CAAA,QAAA,CAAS,KAAK,KAAK,CAAA;EACxB,MAAA,OAAO,QAAQ,OAAQ,EAAA;EAAA;EAGxB,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,KAAK,CAAA;EACxB,IAAA,IAAI,OAAS,EAAA,MAAA,EAAc,KAAA,CAAA,SAAA,CAAU,QAAQ,MAAM,CAAA;EACnD,IAAA,OAAO,KAAM,CAAA,OAAA;EAAA;EACd;EAAA;EAAA;EAAA,EAKO,KAAc,GAAA;EACpB,IAAI,IAAA,IAAA,CAAK,QAAS,CAAA,MAAA,KAAW,CAAG,EAAA;EAChC,IAAI,IAAA,IAAA,CAAK,QAAS,CAAA,MAAA,KAAW,CAAG,EAAA;EAE/B,MAAA,IAAA,CAAK,SAAS,KAAM,EAAA;EACpB,MAAA;EAAA;EAKD,IAAA,IAAA,CAAK,SAAS,KAAM,EAAA;EACpB,IAAK,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,CAAE,GAAI,EAAA;EAAA;EACtB;EAAA;EAAA;EAAA;EAAA,EAMO,QAAiB,GAAA;EAEvB,IAAI,IAAA,IAAA,CAAK,WAAW,CAAG,EAAA;EAIvB,IAAA,KAAA,IAAS,IAAI,CAAG,EAAA,CAAA,GAAI,KAAK,QAAS,CAAA,MAAA,EAAQ,EAAE,CAAG,EAAA;EAC9C,MAAK,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,CAAE,KAAM,EAAA;EAAA;EAGxB,IAAA,IAAA,CAAK,SAAS,MAAS,GAAA,CAAA;EAAA;EAEzB,CAAA;EAzFwB,MAAA,CAAA,WAAA,EAAA,YAAA,CAAA;AAAjB,MAAM,UAAN,GAAA", "file": "index.global.js", "sourcesContent": ["import type { AsyncQueue } from './AsyncQueue';\n\n/**\n * @internal\n */\nexport class AsyncQueueEntry {\n\tpublic readonly promise: Promise<void>;\n\tprivate resolve!: () => void;\n\tprivate reject!: (error: Error) => void;\n\tprivate readonly queue: AsyncQueue;\n\tprivate signal: PolyFillAbortSignal | null = null;\n\tprivate signalListener: (() => void) | null = null;\n\n\tpublic constructor(queue: AsyncQueue) {\n\t\tthis.queue = queue;\n\t\tthis.promise = new Promise((resolve, reject) => {\n\t\t\tthis.resolve = resolve;\n\t\t\tthis.reject = reject;\n\t\t});\n\t}\n\n\tpublic setSignal(signal: AbortSignal) {\n\t\tif (signal.aborted) return this;\n\n\t\tthis.signal = signal as PolyFillAbortSignal;\n\t\tthis.signalListener = () => {\n\t\t\tconst index = this.queue['promises'].indexOf(this);\n\t\t\tif (index !== -1) this.queue['promises'].splice(index, 1);\n\n\t\t\tthis.reject(new Error('Request aborted manually'));\n\t\t};\n\t\tthis.signal.addEventListener('abort', this.signalListener);\n\t\treturn this;\n\t}\n\n\tpublic use() {\n\t\tthis.dispose();\n\t\tthis.resolve();\n\t\treturn this;\n\t}\n\n\tpublic abort() {\n\t\tthis.dispose();\n\t\tthis.reject(new Error('Request aborted manually'));\n\t\treturn this;\n\t}\n\n\tprivate dispose() {\n\t\tif (this.signal) {\n\t\t\tthis.signal.removeEventListener('abort', this.signalListener!);\n\t\t\tthis.signal = null;\n\t\t\tthis.signalListener = null;\n\t\t}\n\t}\n}\n\ninterface PolyFillAbortSignal {\n\treadonly aborted: boolean;\n\taddEventListener(type: 'abort', listener: () => void): void;\n\tremoveEventListener(type: 'abort', listener: () => void): void;\n}\n", "import { AsyncQueueEntry } from './_AsyncQueueEntry';\n\n/**\n * The AsyncQueue class used to sequentialize burst requests\n */\nexport class AsyncQueue {\n\t/**\n\t * The amount of entries in the queue, including the head.\n\t * @seealso {@link queued} for the queued count.\n\t */\n\tpublic get remaining(): number {\n\t\treturn this.promises.length;\n\t}\n\n\t/**\n\t * The amount of queued entries.\n\t * @seealso {@link remaining} for the count with the head.\n\t */\n\tpublic get queued(): number {\n\t\treturn this.remaining === 0 ? 0 : this.remaining - 1;\n\t}\n\n\t/**\n\t * The promises array\n\t */\n\tprivate readonly promises: AsyncQueueEntry[] = [];\n\n\t/**\n\t * Waits for last promise and queues a new one\n\t * @example\n\t * ```typescript\n\t * const queue = new AsyncQueue();\n\t * async function request(url, options) {\n\t *     await queue.wait({ signal: options.signal });\n\t *     try {\n\t *         const result = await fetch(url, options);\n\t *         // Do some operations with 'result'\n\t *     } finally {\n\t *         // Remove first entry from the queue and resolve for the next entry\n\t *         queue.shift();\n\t *     }\n\t * }\n\t *\n\t * request(someUrl1, someOptions1); // Will call fetch() immediately\n\t * request(someUrl2, someOptions2); // Will call fetch() after the first finished\n\t * request(someUrl3, someOptions3); // Will call fetch() after the second finished\n\t * ```\n\t */\n\tpublic wait(options?: Readonly<AsyncQueueWaitOptions>): Promise<void> {\n\t\tconst entry = new AsyncQueueEntry(this);\n\n\t\tif (this.promises.length === 0) {\n\t\t\tthis.promises.push(entry);\n\t\t\treturn Promise.resolve();\n\t\t}\n\n\t\tthis.promises.push(entry);\n\t\tif (options?.signal) entry.setSignal(options.signal);\n\t\treturn entry.promise;\n\t}\n\n\t/**\n\t * Unlocks the head lock and transfers the next lock (if any) to the head.\n\t */\n\tpublic shift(): void {\n\t\tif (this.promises.length === 0) return;\n\t\tif (this.promises.length === 1) {\n\t\t\t// Remove the head entry.\n\t\t\tthis.promises.shift();\n\t\t\treturn;\n\t\t}\n\n\t\t// Remove the head entry, making the 2nd entry the new one.\n\t\t// Then use the head entry, which will unlock the promise.\n\t\tthis.promises.shift();\n\t\tthis.promises[0].use();\n\t}\n\n\t/**\n\t * Aborts all the pending promises.\n\t * @note To avoid race conditions, this does **not** unlock the head lock.\n\t */\n\tpublic abortAll(): void {\n\t\t// If there are no queued entries, skip early.\n\t\tif (this.queued === 0) return;\n\n\t\t// Abort all the entries except the head, that is why the loop starts at\n\t\t// 1 and not at 0.\n\t\tfor (let i = 1; i < this.promises.length; ++i) {\n\t\t\tthis.promises[i].abort();\n\t\t}\n\n\t\tthis.promises.length = 1;\n\t}\n}\n\nexport interface AsyncQueueWaitOptions {\n\tsignal?: AbortSignal | undefined | null;\n}\n"]}