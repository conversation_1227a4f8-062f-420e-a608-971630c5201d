{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/ban-types */\n/* eslint-disable @typescript-eslint/dot-notation */\nfunction validateListener(input: unknown): asserts input is (...args: unknown[]) => void {\n\tif (typeof input !== 'function') {\n\t\tthrow new TypeError(`The listener argument must be a function. Received ${typeof input}`);\n\t}\n}\n\nfunction validateAbortSignal(input: unknown): asserts input is AbortSignal | undefined {\n\t// Only validate that the signal is a signal if its defined\n\tif (input && !(input instanceof AbortSignal)) {\n\t\t// eslint-disable-next-line @typescript-eslint/no-base-to-string\n\t\tthrow new TypeError(`The signal option must be an AbortSignal. Received ${input}`);\n\t}\n}\n\n// Inspired from https://github.com/nodejs/node/blob/42ad967d68137df1a80a877e7b5ad56403fc157f/lib/internal/util.js#L397\nfunction spliceOne(list: unknown[], index: number) {\n\tfor (; index + 1 < list.length; index++) {\n\t\tlist[index] = list[index + 1];\n\t}\n\n\tlist.pop();\n}\n\n// Inspired from https://github.com/nodejs/node/blob/42ad967d68137df1a80a877e7b5ad56403fc157f/lib/events.js#L889\nfunction arrayClone<T extends unknown[]>(arr: T): T {\n\t// At least since V8 8.3, this implementation is faster than the previous\n\t// which always used a simple for-loop\n\tswitch (arr.length) {\n\t\tcase 2:\n\t\t\treturn [arr[0], arr[1]] as T;\n\t\tcase 3:\n\t\t\treturn [arr[0], arr[1], arr[2]] as T;\n\t\tcase 4:\n\t\t\treturn [arr[0], arr[1], arr[2], arr[3]] as T;\n\t\tcase 5:\n\t\t\treturn [arr[0], arr[1], arr[2], arr[3], arr[4]] as T;\n\t\tcase 6:\n\t\t\treturn [arr[0], arr[1], arr[2], arr[3], arr[4], arr[5]] as T;\n\t}\n\n\treturn arr.slice() as T;\n}\n\n// Inspired from https://github.com/nodejs/node/blob/42ad967d68137df1a80a877e7b5ad56403fc157f/lib/events.js#L427-L475\nfunction identicalSequenceRange(a: unknown[], b: unknown[]): [number, number] {\n\tfor (let i = 0; i < a.length - 3; i++) {\n\t\t// Find the first entry of b that matches the current entry of a.\n\t\tconst pos = b.indexOf(a[i]);\n\t\tif (pos !== -1) {\n\t\t\tconst rest = b.length - pos;\n\t\t\tif (rest > 3) {\n\t\t\t\tlet len = 1;\n\t\t\t\tconst maxLen = Math.min(a.length - i, rest);\n\t\t\t\t// Count the number of consecutive entries.\n\t\t\t\twhile (maxLen > len && a[i + len] === b[pos + len]) {\n\t\t\t\t\tlen++;\n\t\t\t\t}\n\t\t\t\tif (len > 3) {\n\t\t\t\t\treturn [len, i];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn [0, 0];\n}\n\nfunction enhanceStackTrace(this: AsyncEventEmitter<any>, err: Error, own: Error) {\n\tlet ctorInfo = '';\n\ttry {\n\t\tconst { name } = this.constructor;\n\t\tif (name !== 'AsyncEventEmitter') ctorInfo = ` on ${name} instance`;\n\t} catch {\n\t\t// Continue regardless of error.\n\t}\n\tconst sep = `\\nEmitted 'error' event${ctorInfo} at:\\n`;\n\n\tconst errStack = err.stack!.split('\\n').slice(1);\n\tconst ownStack = own.stack!.split('\\n').slice(1);\n\n\tconst { 0: len, 1: off } = identicalSequenceRange(ownStack, errStack);\n\tif (len > 0) {\n\t\townStack.splice(off + 1, len - 2, '    [... lines matching original stack trace ...]');\n\t}\n\n\treturn err.stack + sep + ownStack.join('\\n');\n}\n\ninterface InternalEventMap extends Array<Listener> {\n\t_hasWarnedAboutMaxListeners?: boolean;\n}\n\ntype InternalGetAsyncEventEmitterEventParameters<\n\tEE extends AsyncEventEmitter<any>,\n\tEventName extends PropertyKey,\n\tEvents extends Record<PropertyKey, unknown[]> = EE extends AsyncEventEmitter<infer Events> ? Events\n\t:\tRecord<PropertyKey, unknown[]>,\n> =\n\tEventName extends keyof AsyncEventEmitterPredefinedEvents ?\n\t\tEventName extends keyof Events ?\n\t\t\tAsyncEventEmitterPredefinedEvents[EventName] | (Events & Record<PropertyKey, unknown[]>)[EventName]\n\t\t:\tAsyncEventEmitterPredefinedEvents[EventName]\n\t: EventName extends keyof Events ? (Events & Record<PropertyKey, unknown[]>)[EventName]\n\t: any[];\n\nexport type GetAsyncEventEmitterEventParameters<\n\tEE extends AsyncEventEmitter<any>,\n\tEventName extends PropertyKey | keyof AsyncEventEmitterPredefinedEvents,\n> = InternalGetAsyncEventEmitterEventParameters<EE, EventName>;\n\ntype InternalAsyncEventEmitterInternalListenerForEvent<\n\tEE extends AsyncEventEmitter<any>,\n\tEventName extends PropertyKey,\n\tEvents extends Record<PropertyKey, unknown[]> = EE extends AsyncEventEmitter<infer Events> ? Events\n\t:\tRecord<PropertyKey, unknown[]>,\n> =\n\tEventName extends keyof AsyncEventEmitterPredefinedEvents ?\n\t\tEventName extends keyof Events ?\n\t\t\tListener<\n\t\t\t\tAsyncEventEmitterPredefinedEvents[EventName] | (Events & Record<PropertyKey, unknown[]>)[EventName]\n\t\t\t>\n\t\t:\tListener<AsyncEventEmitterPredefinedEvents[EventName]>\n\t: EventName extends keyof Events ? Listener<(Events & Record<PropertyKey, unknown[]>)[EventName]>\n\t: Listener<any[]>;\n\nexport type AsyncEventEmitterInternalListenerForEvent<\n\tEE extends AsyncEventEmitter<any>,\n\tEventName extends PropertyKey | keyof AsyncEventEmitterPredefinedEvents,\n> = InternalAsyncEventEmitterInternalListenerForEvent<EE, EventName>;\n\nexport type AsyncEventEmitterListenerForEvent<\n\tEE extends AsyncEventEmitter<any>,\n\tEventName extends PropertyKey | keyof AsyncEventEmitterPredefinedEvents,\n> = Exclude<AsyncEventEmitterInternalListenerForEvent<EE, EventName>['listener'], undefined>;\n\nconst brandSymbol = Symbol.for('async-event-emitter.ts-brand');\n\nexport class AsyncEventEmitter<Events extends {} = {}> {\n\t/**\n\t * This field doesn't actually exist, it's just a way to make TS properly infer the events from classes that extend AsyncEventEmitter\n\t */\n\tprotected readonly [brandSymbol]!: Events;\n\n\tprivate _events: Record<string | symbol, Listener | InternalEventMap> = {\n\t\t__proto__: null,\n\t} as Record<keyof Events | keyof AsyncEventEmitterPredefinedEvents, Listener | InternalEventMap>;\n\n\tprivate _eventCount = 0;\n\tprivate _maxListeners = 10;\n\tprivate _internalPromiseMap: Map<string, Promise<void>> = new Map();\n\tprivate _wrapperId = 0n;\n\n\tpublic addListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic addListener<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic addListener<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst wrapped = this._wrapListener(eventName, listener, false);\n\n\t\tthis._addListener(eventName, wrapped, false);\n\n\t\treturn this;\n\t}\n\n\tpublic on<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic on<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic on<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this {\n\t\treturn this.addListener(eventName, listener);\n\t}\n\n\tpublic once<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic once<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic once<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst wrapped = this._wrapListener(eventName, listener, true);\n\n\t\tthis._addListener(eventName, wrapped, false);\n\n\t\treturn this;\n\t}\n\n\tpublic removeListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic removeListener<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic removeListener<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst events = this._events;\n\t\tconst eventList = events[eventName];\n\n\t\tif (eventList === undefined) {\n\t\t\treturn this;\n\t\t}\n\n\t\tif (eventList === listener || (eventList as Listener).listener === listener) {\n\t\t\tif (--this._eventCount === 0) {\n\t\t\t\tthis._events = { __proto__: null } as Record<\n\t\t\t\t\tkeyof Events | keyof AsyncEventEmitterPredefinedEvents,\n\t\t\t\t\tListener | InternalEventMap\n\t\t\t\t>;\n\t\t\t} else {\n\t\t\t\tdelete events[eventName];\n\t\t\t\tif (events.removeListener) {\n\t\t\t\t\tthis.emit(\n\t\t\t\t\t\t'removeListener',\n\t\t\t\t\t\teventName as string,\n\t\t\t\t\t\t((eventList as Listener).listener ?? eventList) as (...args: any[]) => void,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (typeof eventList !== 'function') {\n\t\t\tlet position = -1;\n\n\t\t\tfor (let i = eventList.length - 1; i >= 0; i--) {\n\t\t\t\tif (eventList[i] === listener || (eventList[i] as Listener).listener === listener) {\n\t\t\t\t\tposition = i;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (position < 0) {\n\t\t\t\treturn this;\n\t\t\t}\n\n\t\t\tif (position === 0) {\n\t\t\t\teventList.shift();\n\t\t\t} else {\n\t\t\t\tspliceOne(eventList, position);\n\t\t\t}\n\n\t\t\tif (eventList.length === 0) {\n\t\t\t\tdelete events[eventName];\n\t\t\t\t--this._eventCount;\n\t\t\t}\n\n\t\t\tif (events.removeListener !== undefined) {\n\t\t\t\tthis.emit('removeListener', eventName, listener);\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tpublic off<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic off<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic off<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this {\n\t\treturn this.removeListener(eventName, listener);\n\t}\n\n\tpublic removeAllListeners<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(event: K): this;\n\n\tpublic removeAllListeners<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\tevent?: K | undefined,\n\t): this;\n\n\tpublic removeAllListeners(event: string | symbol): this;\n\n\tpublic removeAllListeners(event?: string | symbol | undefined): this;\n\n\tpublic removeAllListeners(event?: string | symbol | undefined): this {\n\t\tconst events = this._events;\n\n\t\t// Not listening for removeListener, no need to emit\n\t\tif (events.removeListener === undefined) {\n\t\t\tif (!event) {\n\t\t\t\tthis._events = { __proto__: null } as Record<\n\t\t\t\t\tkeyof Events | keyof AsyncEventEmitterPredefinedEvents,\n\t\t\t\t\tInternalEventMap\n\t\t\t\t>;\n\t\t\t\tthis._eventCount = 0;\n\t\t\t} else if (events[event] !== undefined) {\n\t\t\t\tif (--this._eventCount === 0) {\n\t\t\t\t\tthis._events = { __proto__: null } as Record<\n\t\t\t\t\t\tkeyof Events | keyof AsyncEventEmitterPredefinedEvents,\n\t\t\t\t\t\tInternalEventMap\n\t\t\t\t\t>;\n\t\t\t\t} else {\n\t\t\t\t\tdelete events[event];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this;\n\t\t}\n\n\t\t// Emit removeListener for all listeners on all events\n\t\tif (!event) {\n\t\t\tfor (const key of Reflect.ownKeys(events) as (keyof Events | keyof AsyncEventEmitterPredefinedEvents)[]) {\n\t\t\t\tif (key === 'removeListener') {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthis.removeAllListeners(key);\n\t\t\t}\n\n\t\t\tthis.removeAllListeners('removeListener');\n\t\t\tthis._events = { __proto__: null } as Record<\n\t\t\t\tkeyof Events | keyof AsyncEventEmitterPredefinedEvents,\n\t\t\t\tInternalEventMap\n\t\t\t>;\n\t\t\tthis._eventCount = 0;\n\n\t\t\treturn this;\n\t\t}\n\n\t\tconst listeners = events[event];\n\n\t\tif (typeof listeners === 'function') {\n\t\t\tthis.removeListener(event, listeners as any);\n\t\t} else if (listeners !== undefined) {\n\t\t\t// LIFO order\n\t\t\tfor (let i = listeners.length - 1; i >= 0; i--) {\n\t\t\t\tthis.removeListener(event, listeners[i] as any);\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tpublic setMaxListeners(n: number): this {\n\t\tif (typeof n !== 'number' || n < 0 || Number.isNaN(n)) {\n\t\t\tthrow new RangeError(`Expected to get a non-negative number for \"setMaxListeners\", got ${n} instead`);\n\t\t}\n\n\t\tthis._maxListeners = n;\n\n\t\treturn this;\n\t}\n\n\tpublic getMaxListeners(): number {\n\t\treturn this._maxListeners;\n\t}\n\n\tpublic listeners<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t): AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>[];\n\n\tpublic listeners<K extends string | symbol>(\n\t\teventName: K,\n\t): AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>[];\n\n\tpublic listeners<K extends string | symbol>(\n\t\teventName: K,\n\t): AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>[] {\n\t\tconst eventList = this._events[eventName];\n\n\t\tif (eventList === undefined) {\n\t\t\treturn [];\n\t\t}\n\n\t\tif (typeof eventList === 'function') {\n\t\t\treturn [eventList.listener ?? eventList] as AsyncEventEmitterListenerForEvent<\n\t\t\t\tAsyncEventEmitter<Events>,\n\t\t\t\tK\n\t\t\t>[];\n\t\t}\n\n\t\tconst ret = arrayClone(eventList) as AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>[];\n\n\t\tfor (let i = 0; i < ret.length; ++i) {\n\t\t\tconst orig = (ret[i] as Listener).listener;\n\t\t\tif (typeof orig === 'function') {\n\t\t\t\tret[i] = orig as AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>;\n\t\t\t}\n\t\t}\n\n\t\treturn ret;\n\t}\n\n\tpublic rawListeners<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t): AsyncEventEmitterInternalListenerForEvent<AsyncEventEmitter<Events>, K>[];\n\n\tpublic rawListeners<K extends string | symbol>(\n\t\teventName: K,\n\t): AsyncEventEmitterInternalListenerForEvent<AsyncEventEmitter<Events>, K>[];\n\n\tpublic rawListeners<K extends string | symbol>(\n\t\teventName: K,\n\t): AsyncEventEmitterInternalListenerForEvent<AsyncEventEmitter<Events>, K>[] {\n\t\tconst eventList = this._events[eventName];\n\n\t\tif (eventList === undefined) {\n\t\t\treturn [];\n\t\t}\n\n\t\tif (typeof eventList === 'function') {\n\t\t\treturn [eventList] as AsyncEventEmitterInternalListenerForEvent<AsyncEventEmitter<Events>, K>[];\n\t\t}\n\n\t\treturn arrayClone(eventList) as AsyncEventEmitterInternalListenerForEvent<AsyncEventEmitter<Events>, K>[];\n\t}\n\n\tpublic emit<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\t...args: GetAsyncEventEmitterEventParameters<AsyncEventEmitter<Events>, K>\n\t): boolean;\n\n\tpublic emit<K extends string | symbol>(\n\t\teventName: K,\n\t\t...args: GetAsyncEventEmitterEventParameters<AsyncEventEmitter<Events>, K>\n\t): boolean;\n\n\tpublic emit<K extends string | symbol>(\n\t\teventName: K,\n\t\t...args: GetAsyncEventEmitterEventParameters<AsyncEventEmitter<Events>, K>\n\t): boolean {\n\t\tlet doError = eventName === 'error';\n\n\t\tconst events = this._events;\n\t\tif (events !== undefined) {\n\t\t\tdoError = doError && events.error === undefined;\n\t\t} else if (!doError) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (doError) {\n\t\t\tlet er: unknown;\n\n\t\t\tif (args.length > 0) {\n\t\t\t\t// eslint-disable-next-line prefer-destructuring\n\t\t\t\ter = args[0];\n\t\t\t}\n\n\t\t\tif (er instanceof Error) {\n\t\t\t\ttry {\n\t\t\t\t\tconst capture = {};\n\t\t\t\t\t// eslint-disable-next-line @typescript-eslint/unbound-method\n\t\t\t\t\tError.captureStackTrace(capture, AsyncEventEmitter.prototype.emit);\n\t\t\t\t\tObject.defineProperty(er, 'stack', {\n\t\t\t\t\t\tvalue: enhanceStackTrace.call(this, er, capture as Error),\n\t\t\t\t\t\tconfigurable: true,\n\t\t\t\t\t});\n\t\t\t\t} catch {\n\t\t\t\t\t// Continue regardless of error\n\t\t\t\t}\n\n\t\t\t\tthrow er; // Unhandled 'error' event\n\t\t\t}\n\n\t\t\tconst stringifiedError = String(er);\n\n\t\t\t// Give some error to user\n\t\t\tconst err = new Error(`Unhandled 'error' event emitted, received ${stringifiedError}`);\n\t\t\t// @ts-expect-error Add context to error too\n\t\t\terr.context = er;\n\n\t\t\tthrow err; // Unhandled 'error' event\n\t\t}\n\n\t\tconst handlers = events[eventName];\n\n\t\tif (handlers === undefined) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (typeof handlers === 'function') {\n\t\t\tconst result = handlers.apply(this, args);\n\n\t\t\tif (result !== undefined && result !== null) {\n\t\t\t\thandleMaybeAsync(this, result);\n\t\t\t}\n\t\t} else {\n\t\t\tconst len = handlers.length;\n\t\t\tconst listeners = arrayClone(handlers as InternalEventMap);\n\n\t\t\tfor (let i = 0; i < len; ++i) {\n\t\t\t\t// We call all listeners regardless of the result, as we already handle possible error emits in the wrapped func\n\t\t\t\tconst result = listeners[i].apply(this, args);\n\n\t\t\t\tif (result !== undefined && result !== null) {\n\t\t\t\t\thandleMaybeAsync(this, result);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n\tpublic listenerCount<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(eventName: K): number;\n\n\tpublic listenerCount(eventName: string | symbol): number;\n\n\tpublic listenerCount(eventName: string | symbol): number {\n\t\tconst events = this._events;\n\n\t\tif (events === undefined) {\n\t\t\treturn 0;\n\t\t}\n\n\t\tconst eventListeners = events[eventName];\n\n\t\tif (typeof eventListeners === 'function') {\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn eventListeners?.length ?? 0;\n\t}\n\n\tpublic prependListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic prependListener<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic prependListener<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst wrapped = this._wrapListener(eventName, listener, false);\n\n\t\tthis._addListener(eventName, wrapped, true);\n\n\t\treturn this;\n\t}\n\n\tpublic prependOnceListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic prependOnceListener<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this;\n\n\tpublic prependOnceListener<K extends string | symbol>(\n\t\teventName: K,\n\t\tlistener: AsyncEventEmitterListenerForEvent<AsyncEventEmitter<Events>, K>,\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst wrapped = this._wrapListener(eventName, listener, true);\n\n\t\tthis._addListener(eventName, wrapped, true);\n\n\t\treturn this;\n\t}\n\n\tpublic eventNames(): (string | symbol)[] & (keyof AsyncEventEmitterPredefinedEvents)[] & (keyof Events)[] {\n\t\treturn this._eventCount > 0 ?\n\t\t\t\t(Reflect.ownKeys(this._events) as (string | symbol)[] &\n\t\t\t\t\t(keyof AsyncEventEmitterPredefinedEvents)[] &\n\t\t\t\t\t(keyof Events)[])\n\t\t\t:\t[];\n\t}\n\n\tpublic async waitForAllListenersToComplete() {\n\t\tconst promises = [...this._internalPromiseMap.values()];\n\n\t\tif (promises.length === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tawait Promise.all(promises);\n\n\t\treturn true;\n\t}\n\n\tprivate _addListener(eventName: PropertyKey, wrappedListener: Listener, prepend: boolean) {\n\t\t// Emit newListener first in the event someone is listening for it\n\t\tif (this._events.newListener !== undefined) {\n\t\t\t// Thanks TypeScript for the cast... now with more what the fuck\n\t\t\tthis.emit(\n\t\t\t\t'newListener',\n\t\t\t\teventName as string | symbol,\n\t\t\t\t((wrappedListener as Listener).listener ?? wrappedListener) as (...args: any[]) => void,\n\t\t\t);\n\t\t}\n\n\t\tlet existing = this._events[eventName];\n\n\t\tif (existing === undefined) {\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\texisting = this._events[eventName] = wrappedListener;\n\t\t\t++this._eventCount;\n\t\t} else if (typeof existing === 'function') {\n\t\t\t// Adding the second element, need to change to array.\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\texisting = this._events[eventName] = prepend ? [wrappedListener, existing] : [existing, wrappedListener];\n\t\t\t// If we've already got an array, just append.\n\t\t} else if (prepend) {\n\t\t\texisting.unshift(wrappedListener);\n\t\t} else {\n\t\t\texisting.push(wrappedListener);\n\t\t}\n\n\t\tconst existingWarnedAboutMaxListeners = Reflect.get(existing, '_hasWarnedAboutMaxListeners') as boolean;\n\n\t\tif (this._maxListeners > 0 && existing.length > this._maxListeners && !existingWarnedAboutMaxListeners) {\n\t\t\tReflect.set(existing, '_hasWarnedAboutMaxListeners', true);\n\t\t\tconst warningMessage = [\n\t\t\t\t`Possible AsyncEventEmitter memory leak detected. ${existing.length} ${String(\n\t\t\t\t\teventName,\n\t\t\t\t)} listeners added to ${this.constructor.name}.`,\n\t\t\t\t`Use emitter.setMaxListeners() to increase the limit.`,\n\t\t\t].join(' ');\n\t\t\tconsole.warn(warningMessage);\n\t\t}\n\t}\n\n\tprivate _wrapListener(eventName: PropertyKey, listener: (...args: any[]) => void, once: boolean): Listener {\n\t\tif (!once) {\n\t\t\treturn listener as Listener;\n\t\t}\n\n\t\tconst state = {\n\t\t\tfired: false,\n\t\t\twrapFn: undefined!,\n\t\t\teventEmitter: this,\n\t\t\teventName,\n\t\t\tlistener,\n\t\t} as WrappedOnceState<any[]>;\n\n\t\tconst aliased = onceWrapper<any[]>;\n\n\t\tconst wrapped = aliased.bind(state) as Listener<any[]>;\n\t\twrapped.listener = listener as Listener<any[]>;\n\t\tstate.wrapFn = wrapped;\n\n\t\treturn wrapped as Listener;\n\t}\n\n\tpublic static listenerCount<\n\t\tEventMap extends {},\n\t\tEventName extends PropertyKey = keyof EventMap | keyof AsyncEventEmitterPredefinedEvents,\n\t>(emitter: AsyncEventEmitter<EventMap>, eventName: EventName | keyof AsyncEventEmitterPredefinedEvents): number;\n\n\tpublic static listenerCount(emitter: AsyncEventEmitter<any>, eventName: string | symbol): number;\n\n\tpublic static listenerCount(emitter: AsyncEventEmitter<any>, eventName: string | symbol) {\n\t\treturn emitter.listenerCount(eventName);\n\t}\n\n\tpublic static async once<\n\t\tEventMap extends {},\n\t\tEventName extends PropertyKey = keyof EventMap | keyof AsyncEventEmitterPredefinedEvents,\n\t>(\n\t\temitter: AsyncEventEmitter<EventMap>,\n\t\teventName: EventName,\n\t\toptions?: AbortableMethods,\n\t): Promise<GetAsyncEventEmitterEventParameters<AsyncEventEmitter<EventMap>, EventName>>;\n\n\tpublic static async once(\n\t\temitter: AsyncEventEmitter<any>,\n\t\teventName: string | symbol,\n\t\toptions?: AbortableMethods,\n\t): Promise<any[]>;\n\n\tpublic static async once(\n\t\temitter: AsyncEventEmitter<any>,\n\t\teventName: string | symbol,\n\t\toptions: AbortableMethods = {},\n\t) {\n\t\tconst signal = options?.signal;\n\t\tvalidateAbortSignal(signal);\n\n\t\tif (signal?.aborted) {\n\t\t\tthrow new AbortError(undefined, { cause: getReason(signal) });\n\t\t}\n\n\t\treturn new Promise<any[]>((resolve, reject) => {\n\t\t\tconst errorListener = (err: unknown) => {\n\t\t\t\temitter.removeListener(eventName, resolver);\n\n\t\t\t\tif (signal) {\n\t\t\t\t\teventTargetAgnosticRemoveListener(emitter, eventName, abortListener);\n\t\t\t\t}\n\n\t\t\t\treject(err);\n\t\t\t};\n\n\t\t\tconst resolver = (...args: any[]) => {\n\t\t\t\temitter.removeListener('error', errorListener);\n\n\t\t\t\tif (signal) {\n\t\t\t\t\teventTargetAgnosticRemoveListener(signal, 'abort', abortListener);\n\t\t\t\t}\n\n\t\t\t\tresolve(args as any[]);\n\t\t\t};\n\n\t\t\temitter.once(eventName, resolver);\n\t\t\tif (eventName !== 'error') {\n\t\t\t\temitter.once('error', errorListener);\n\t\t\t}\n\n\t\t\tconst abortListener = () => {\n\t\t\t\teventTargetAgnosticRemoveListener(emitter, eventName, resolver);\n\t\t\t\teventTargetAgnosticRemoveListener(emitter, 'error', errorListener);\n\t\t\t\treject(new AbortError(undefined, { cause: getReason(signal) }));\n\t\t\t};\n\n\t\t\tif (signal) {\n\t\t\t\teventTargetAgnosticAddListener(signal, 'abort', abortListener, { once: true });\n\t\t\t}\n\t\t});\n\t}\n\n\tpublic static on<\n\t\tEventMap extends {},\n\t\tEventName extends PropertyKey = keyof EventMap | keyof AsyncEventEmitterPredefinedEvents,\n\t>(\n\t\temitter: AsyncEventEmitter<EventMap>,\n\t\teventName: EventName,\n\t\toptions?: AbortableMethods,\n\t): AsyncGenerator<GetAsyncEventEmitterEventParameters<AsyncEventEmitter<EventMap>, EventName>, void>;\n\n\tpublic static on(\n\t\temitter: AsyncEventEmitter<any>,\n\t\teventName: string | symbol,\n\t\toptions?: AbortableMethods,\n\t): AsyncGenerator<any[], void>;\n\n\tpublic static on(\n\t\temitter: AsyncEventEmitter<any>,\n\t\teventName: string | symbol,\n\t\toptions: AbortableMethods = {},\n\t): AsyncGenerator<any[], void> {\n\t\tconst signal = options?.signal;\n\t\tvalidateAbortSignal(signal);\n\n\t\tif (signal?.aborted) {\n\t\t\tthrow new AbortError(undefined, { cause: getReason(signal) });\n\t\t}\n\n\t\tconst unconsumedEvents: unknown[][] = [];\n\t\tconst unconsumedPromises: { resolve: (value?: unknown) => void; reject: (reason?: unknown) => void }[] = [];\n\t\tlet error: unknown = null;\n\t\tlet finished = false;\n\n\t\tconst abortListener = () => {\n\t\t\terrorHandler(new AbortError(undefined, { cause: getReason(signal) }));\n\t\t};\n\n\t\tconst eventHandler = (...args: unknown[]) => {\n\t\t\tconst promise = unconsumedPromises.shift();\n\t\t\tif (promise) {\n\t\t\t\tpromise.resolve(createIterResult(args, false));\n\t\t\t} else {\n\t\t\t\tunconsumedEvents.push(args);\n\t\t\t}\n\t\t};\n\n\t\tconst errorHandler = (err: unknown) => {\n\t\t\tfinished = true;\n\n\t\t\tconst toError = unconsumedPromises.shift();\n\n\t\t\tif (toError) {\n\t\t\t\ttoError.reject(err);\n\t\t\t} else {\n\t\t\t\terror = err;\n\t\t\t}\n\n\t\t\tvoid iterator.return();\n\t\t};\n\n\t\tconst iterator: AsyncGenerator<any[], void> = Object.setPrototypeOf(\n\t\t\t{\n\t\t\t\tnext() {\n\t\t\t\t\t// First, we consume all unread events\n\t\t\t\t\tconst value = unconsumedEvents.shift();\n\t\t\t\t\tif (value) {\n\t\t\t\t\t\treturn Promise.resolve(createIterResult(value, false));\n\t\t\t\t\t}\n\n\t\t\t\t\t// Then we error, if an error happened\n\t\t\t\t\t// This happens one time if at all, because after 'error'\n\t\t\t\t\t// we stop listening\n\t\t\t\t\tif (error) {\n\t\t\t\t\t\tconst p = Promise.reject(error);\n\t\t\t\t\t\t// Only the first element errors\n\t\t\t\t\t\terror = null;\n\t\t\t\t\t\treturn p;\n\t\t\t\t\t}\n\n\t\t\t\t\t// If the iterator is finished, resolve to done\n\t\t\t\t\tif (finished) {\n\t\t\t\t\t\treturn Promise.resolve(createIterResult(undefined, true));\n\t\t\t\t\t}\n\n\t\t\t\t\t// Wait until an event happens\n\t\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\t\tunconsumedPromises.push({ resolve, reject });\n\t\t\t\t\t});\n\t\t\t\t},\n\n\t\t\t\treturn() {\n\t\t\t\t\temitter.off(eventName, eventHandler);\n\t\t\t\t\temitter.off('error', errorHandler);\n\n\t\t\t\t\tif (signal) {\n\t\t\t\t\t\teventTargetAgnosticRemoveListener(signal, 'abort', abortListener);\n\t\t\t\t\t}\n\n\t\t\t\t\tfinished = true;\n\n\t\t\t\t\tconst doneResult = createIterResult(undefined, true);\n\t\t\t\t\tfor (const promise of unconsumedPromises) {\n\t\t\t\t\t\tpromise.resolve(doneResult);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Promise.resolve(doneResult);\n\t\t\t\t},\n\n\t\t\t\tthrow(err: unknown) {\n\t\t\t\t\tif (!err || !(err instanceof Error)) {\n\t\t\t\t\t\tthrow new TypeError(\n\t\t\t\t\t\t\t`Expected Error instance to be thrown in AsyncEventEmitter.AsyncIterator. Got ${err}`,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\terror = err;\n\t\t\t\t\temitter.off(eventName, eventHandler);\n\t\t\t\t\temitter.off('error', errorHandler);\n\t\t\t\t},\n\n\t\t\t\t[Symbol.asyncIterator]() {\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\t\t\t},\n\t\t\tAsyncIteratorPrototype,\n\t\t);\n\n\t\temitter.on(eventName, eventHandler);\n\t\tif (eventName !== 'error') {\n\t\t\temitter.on('error', errorHandler);\n\t\t}\n\n\t\tif (signal) {\n\t\t\teventTargetAgnosticAddListener(signal, 'abort', abortListener);\n\t\t}\n\n\t\treturn iterator;\n\t}\n}\n\nexport interface AsyncEventEmitterPredefinedEvents {\n\tnewListener: [eventName: string | symbol, listener: (...args: any[]) => void];\n\tremoveListener: [eventName: string | symbol, listener: (...args: any[]) => void];\n}\n\ninterface WrappedOnceState<Args extends unknown[] = unknown[]> {\n\tlistener: (...args: Args) => void;\n\tfired: boolean;\n\teventName: string | symbol;\n\teventEmitter: AsyncEventEmitter<any>;\n\twrapFn: (...args: Args) => void;\n}\n\nexport interface Listener<Args extends any[] = any[]> {\n\t(...args: Args): void;\n\tlistener?: (...args: Args) => void;\n\t// _hasWarnedAboutMaxListeners?: boolean;\n}\n\nexport interface AbortableMethods {\n\tsignal?: AbortSignal;\n}\n\n// @ts-ignore Not all paths returning is fine just fine:tm:\nfunction onceWrapper<Args extends any[] = any[]>(this: WrappedOnceState<Args>) {\n\tif (!this.fired) {\n\t\tthis.eventEmitter.removeListener(this.eventName, this.wrapFn);\n\t\tthis.fired = true;\n\t\t// eslint-disable-next-line @typescript-eslint/dot-notation\n\t\tif (arguments.length === 0) {\n\t\t\t// @ts-expect-error Types can be hell\n\t\t\treturn this.listener.call(this.eventEmitter);\n\t\t}\n\n\t\t// eslint-disable-next-line prefer-rest-params\n\t\treturn this.listener.apply(this.eventEmitter, arguments as unknown as Args);\n\t}\n}\n\n/**\n * A TypeScript not-compliant way of accessing AbortSignal#reason\n * Because DOM types have it, NodeJS types don't. -w-\n */\nfunction getReason(signal: any) {\n\treturn signal?.reason;\n}\n\nfunction eventTargetAgnosticRemoveListener(\n\temitter: any,\n\tname: PropertyKey,\n\tlistener: (...args: unknown[]) => any,\n\tflags?: InternalAgnosticFlags,\n) {\n\tif (typeof emitter.off === 'function') {\n\t\temitter.off(name, listener);\n\t} else if (typeof emitter.removeEventListener === 'function') {\n\t\temitter.removeEventListener(name, listener, flags);\n\t}\n}\n\nfunction eventTargetAgnosticAddListener(\n\temitter: any,\n\tname: string | symbol,\n\tlistener: (...args: unknown[]) => any,\n\tflags?: InternalAgnosticFlags,\n) {\n\tif (typeof emitter.on === 'function') {\n\t\tif (flags?.once) {\n\t\t\temitter.once!(name, listener);\n\t\t} else {\n\t\t\temitter.on(name, listener);\n\t\t}\n\t} else if (typeof emitter.addEventListener === 'function') {\n\t\temitter.addEventListener(name, listener, flags);\n\t}\n}\n\ninterface InternalAgnosticFlags {\n\tonce?: boolean;\n}\n\n// eslint-disable-next-line func-names, @typescript-eslint/no-empty-function\nconst AsyncIteratorPrototype = Object.getPrototypeOf(Object.getPrototypeOf(async function* () {}).prototype);\n\nfunction createIterResult(value: unknown, done: boolean) {\n\treturn { value, done };\n}\n\nexport interface AbortErrorOptions {\n\tcause?: unknown;\n}\n\nexport class AbortError extends Error {\n\tpublic readonly code = 'ABORT_ERR';\n\tpublic override readonly name = 'AbortError';\n\n\tpublic constructor(message = 'The operation was aborted', options: AbortErrorOptions | undefined = undefined) {\n\t\tif (options !== undefined && typeof options !== 'object') {\n\t\t\tthrow new TypeError(`Failed to create AbortError: options is not an object or undefined`);\n\t\t}\n\n\t\tsuper(message, options);\n\t}\n}\n\nfunction handleMaybeAsync(emitter: AsyncEventEmitter<any>, result: any) {\n\ttry {\n\t\tconst the = result.then;\n\t\tconst fin = result.finally;\n\n\t\tif (typeof the === 'function') {\n\t\t\tthe.call(result, undefined, (error: any) => {\n\t\t\t\t// Emit on next tick\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\temitter.emit('error', error);\n\t\t\t\t}, 0);\n\t\t\t});\n\t\t}\n\n\t\tif (typeof fin === 'function') {\n\t\t\tconst promiseId = String(++emitter['_wrapperId']);\n\t\t\temitter['_internalPromiseMap'].set(promiseId, result);\n\t\t\tfin.call(result, function final() {\n\t\t\t\temitter['_internalPromiseMap'].delete(promiseId);\n\t\t\t});\n\t\t}\n\t} catch (err) {\n\t\temitter.emit('error', err);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,WAAS,iBAAiB,OAA+D;AACxF,QAAI,OAAO,UAAU,YAAY;AAChC,YAAM,IAAI,UAAU,sDAAsD,OAAO,KAAK,EAAE;AAAA,IACzF;AAAA,EACD;AAJS;AAMT,WAAS,oBAAoB,OAA0D;AAEtF,QAAI,SAAS,EAAE,iBAAiB,cAAc;AAE7C,YAAM,IAAI,UAAU,sDAAsD,KAAK,EAAE;AAAA,IAClF;AAAA,EACD;AANS;AAST,WAAS,UAAU,MAAiB,OAAe;AAClD,WAAO,QAAQ,IAAI,KAAK,QAAQ,SAAS;AACxC,WAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAAA,IAC7B;AAEA,SAAK,IAAI;AAAA,EACV;AANS;AAST,WAAS,WAAgC,KAAW;AAGnD,YAAQ,IAAI,QAAQ;AAAA,MACnB,KAAK;AACJ,eAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,MACvB,KAAK;AACJ,eAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,MAC/B,KAAK;AACJ,eAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,MACvC,KAAK;AACJ,eAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,MAC/C,KAAK;AACJ,eAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,IACxD;AAEA,WAAO,IAAI,MAAM;AAAA,EAClB;AAjBS;AAoBT,WAAS,uBAAuB,GAAc,GAAgC;AAC7E,aAAS,IAAI,GAAG,IAAI,EAAE,SAAS,GAAG,KAAK;AAEtC,YAAM,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC1B,UAAI,QAAQ,IAAI;AACf,cAAM,OAAO,EAAE,SAAS;AACxB,YAAI,OAAO,GAAG;AACb,cAAI,MAAM;AACV,gBAAM,SAAS,KAAK,IAAI,EAAE,SAAS,GAAG,IAAI;AAE1C,iBAAO,SAAS,OAAO,EAAE,IAAI,GAAG,MAAM,EAAE,MAAM,GAAG,GAAG;AACnD;AAAA,UACD;AACA,cAAI,MAAM,GAAG;AACZ,mBAAO,CAAC,KAAK,CAAC;AAAA,UACf;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO,CAAC,GAAG,CAAC;AAAA,EACb;AArBS;AAuBT,WAAS,kBAAgD,KAAY,KAAY;AAChF,QAAI,WAAW;AACf,QAAI;AACH,YAAM,EAAE,KAAK,IAAI,KAAK;AACtB,UAAI,SAAS,oBAAqB,YAAW,OAAO,IAAI;AAAA,IACzD,QAAQ;AAAA,IAER;AACA,UAAM,MAAM;AAAA,uBAA0B,QAAQ;AAAA;AAE9C,UAAM,WAAW,IAAI,MAAO,MAAM,IAAI,EAAE,MAAM,CAAC;AAC/C,UAAM,WAAW,IAAI,MAAO,MAAM,IAAI,EAAE,MAAM,CAAC;AAE/C,UAAM,EAAE,GAAG,KAAK,GAAG,IAAI,IAAI,uBAAuB,UAAU,QAAQ;AACpE,QAAI,MAAM,GAAG;AACZ,eAAS,OAAO,MAAM,GAAG,MAAM,GAAG,mDAAmD;AAAA,IACtF;AAEA,WAAO,IAAI,QAAQ,MAAM,SAAS,KAAK,IAAI;AAAA,EAC5C;AAnBS;AAoET,MAAM,cAAc,OAAO,IAAI,8BAA8B;AAMxC;AAJd,MAAM,qBAAN,MAAM,mBAA0C;AAAA,IAAhD;AAMN,WAAQ,UAAgE;AAAA,QACvE,WAAW;AAAA,MACZ;AAEA,WAAQ,cAAc;AACtB,WAAQ,gBAAgB;AACxB,WAAQ,sBAAkD,oBAAI,IAAI;AAClE,WAAQ,aAAa;AAAA;AAAA,IAYd,YACN,WACA,UACO;AACP,uBAAiB,QAAQ;AAEzB,YAAM,UAAU,KAAK,cAAc,WAAW,UAAU,KAAK;AAE7D,WAAK,aAAa,WAAW,SAAS,KAAK;AAE3C,aAAO;AAAA,IACR;AAAA,IAYO,GACN,WACA,UACO;AACP,aAAO,KAAK,YAAY,WAAW,QAAQ;AAAA,IAC5C;AAAA,IAYO,KACN,WACA,UACO;AACP,uBAAiB,QAAQ;AAEzB,YAAM,UAAU,KAAK,cAAc,WAAW,UAAU,IAAI;AAE5D,WAAK,aAAa,WAAW,SAAS,KAAK;AAE3C,aAAO;AAAA,IACR;AAAA,IAYO,eACN,WACA,UACO;AACP,uBAAiB,QAAQ;AAEzB,YAAM,SAAS,KAAK;AACpB,YAAM,YAAY,OAAO,SAAS;AAElC,UAAI,cAAc,QAAW;AAC5B,eAAO;AAAA,MACR;AAEA,UAAI,cAAc,YAAa,UAAuB,aAAa,UAAU;AAC5E,YAAI,EAAE,KAAK,gBAAgB,GAAG;AAC7B,eAAK,UAAU,EAAE,WAAW,KAAK;AAAA,QAIlC,OAAO;AACN,iBAAO,OAAO,SAAS;AACvB,cAAI,OAAO,gBAAgB;AAC1B,iBAAK;AAAA,cACJ;AAAA,cACA;AAAA,cACE,UAAuB,YAAY;AAAA,YACtC;AAAA,UACD;AAAA,QACD;AAAA,MACD,WAAW,OAAO,cAAc,YAAY;AAC3C,YAAI,WAAW;AAEf,iBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,cAAI,UAAU,CAAC,MAAM,YAAa,UAAU,CAAC,EAAe,aAAa,UAAU;AAClF,uBAAW;AACX;AAAA,UACD;AAAA,QACD;AAEA,YAAI,WAAW,GAAG;AACjB,iBAAO;AAAA,QACR;AAEA,YAAI,aAAa,GAAG;AACnB,oBAAU,MAAM;AAAA,QACjB,OAAO;AACN,oBAAU,WAAW,QAAQ;AAAA,QAC9B;AAEA,YAAI,UAAU,WAAW,GAAG;AAC3B,iBAAO,OAAO,SAAS;AACvB,YAAE,KAAK;AAAA,QACR;AAEA,YAAI,OAAO,mBAAmB,QAAW;AACxC,eAAK,KAAK,kBAAkB,WAAW,QAAQ;AAAA,QAChD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA,IAYO,IACN,WACA,UACO;AACP,aAAO,KAAK,eAAe,WAAW,QAAQ;AAAA,IAC/C;AAAA,IAYO,mBAAmB,OAA2C;AACpE,YAAM,SAAS,KAAK;AAGpB,UAAI,OAAO,mBAAmB,QAAW;AACxC,YAAI,CAAC,OAAO;AACX,eAAK,UAAU,EAAE,WAAW,KAAK;AAIjC,eAAK,cAAc;AAAA,QACpB,WAAW,OAAO,KAAK,MAAM,QAAW;AACvC,cAAI,EAAE,KAAK,gBAAgB,GAAG;AAC7B,iBAAK,UAAU,EAAE,WAAW,KAAK;AAAA,UAIlC,OAAO;AACN,mBAAO,OAAO,KAAK;AAAA,UACpB;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAGA,UAAI,CAAC,OAAO;AACX,mBAAW,OAAO,QAAQ,QAAQ,MAAM,GAAiE;AACxG,cAAI,QAAQ,kBAAkB;AAC7B;AAAA,UACD;AACA,eAAK,mBAAmB,GAAG;AAAA,QAC5B;AAEA,aAAK,mBAAmB,gBAAgB;AACxC,aAAK,UAAU,EAAE,WAAW,KAAK;AAIjC,aAAK,cAAc;AAEnB,eAAO;AAAA,MACR;AAEA,YAAM,YAAY,OAAO,KAAK;AAE9B,UAAI,OAAO,cAAc,YAAY;AACpC,aAAK,eAAe,OAAO,SAAgB;AAAA,MAC5C,WAAW,cAAc,QAAW;AAEnC,iBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,eAAK,eAAe,OAAO,UAAU,CAAC,CAAQ;AAAA,QAC/C;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA,IAEO,gBAAgB,GAAiB;AACvC,UAAI,OAAO,MAAM,YAAY,IAAI,KAAK,OAAO,MAAM,CAAC,GAAG;AACtD,cAAM,IAAI,WAAW,oEAAoE,CAAC,UAAU;AAAA,MACrG;AAEA,WAAK,gBAAgB;AAErB,aAAO;AAAA,IACR;AAAA,IAEO,kBAA0B;AAChC,aAAO,KAAK;AAAA,IACb;AAAA,IAUO,UACN,WACoE;AACpE,YAAM,YAAY,KAAK,QAAQ,SAAS;AAExC,UAAI,cAAc,QAAW;AAC5B,eAAO,CAAC;AAAA,MACT;AAEA,UAAI,OAAO,cAAc,YAAY;AACpC,eAAO,CAAC,UAAU,YAAY,SAAS;AAAA,MAIxC;AAEA,YAAM,MAAM,WAAW,SAAS;AAEhC,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACpC,cAAM,OAAQ,IAAI,CAAC,EAAe;AAClC,YAAI,OAAO,SAAS,YAAY;AAC/B,cAAI,CAAC,IAAI;AAAA,QACV;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA,IAUO,aACN,WAC4E;AAC5E,YAAM,YAAY,KAAK,QAAQ,SAAS;AAExC,UAAI,cAAc,QAAW;AAC5B,eAAO,CAAC;AAAA,MACT;AAEA,UAAI,OAAO,cAAc,YAAY;AACpC,eAAO,CAAC,SAAS;AAAA,MAClB;AAEA,aAAO,WAAW,SAAS;AAAA,IAC5B;AAAA,IAYO,KACN,cACG,MACO;AACV,UAAI,UAAU,cAAc;AAE5B,YAAM,SAAS,KAAK;AACpB,UAAI,WAAW,QAAW;AACzB,kBAAU,WAAW,OAAO,UAAU;AAAA,MACvC,WAAW,CAAC,SAAS;AACpB,eAAO;AAAA,MACR;AAEA,UAAI,SAAS;AACZ,YAAI;AAEJ,YAAI,KAAK,SAAS,GAAG;AAEpB,eAAK,KAAK,CAAC;AAAA,QACZ;AAEA,YAAI,cAAc,OAAO;AACxB,cAAI;AACH,kBAAM,UAAU,CAAC;AAEjB,kBAAM,kBAAkB,SAAS,mBAAkB,UAAU,IAAI;AACjE,mBAAO,eAAe,IAAI,SAAS;AAAA,cAClC,OAAO,kBAAkB,KAAK,MAAM,IAAI,OAAgB;AAAA,cACxD,cAAc;AAAA,YACf,CAAC;AAAA,UACF,QAAQ;AAAA,UAER;AAEA,gBAAM;AAAA,QACP;AAEA,cAAM,mBAAmB,OAAO,EAAE;AAGlC,cAAM,MAAM,IAAI,MAAM,6CAA6C,gBAAgB,EAAE;AAErF,YAAI,UAAU;AAEd,cAAM;AAAA,MACP;AAEA,YAAM,WAAW,OAAO,SAAS;AAEjC,UAAI,aAAa,QAAW;AAC3B,eAAO;AAAA,MACR;AAEA,UAAI,OAAO,aAAa,YAAY;AACnC,cAAM,SAAS,SAAS,MAAM,MAAM,IAAI;AAExC,YAAI,WAAW,UAAa,WAAW,MAAM;AAC5C,2BAAiB,MAAM,MAAM;AAAA,QAC9B;AAAA,MACD,OAAO;AACN,cAAM,MAAM,SAAS;AACrB,cAAM,YAAY,WAAW,QAA4B;AAEzD,iBAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAE7B,gBAAM,SAAS,UAAU,CAAC,EAAE,MAAM,MAAM,IAAI;AAE5C,cAAI,WAAW,UAAa,WAAW,MAAM;AAC5C,6BAAiB,MAAM,MAAM;AAAA,UAC9B;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA,IAMO,cAAc,WAAoC;AACxD,YAAM,SAAS,KAAK;AAEpB,UAAI,WAAW,QAAW;AACzB,eAAO;AAAA,MACR;AAEA,YAAM,iBAAiB,OAAO,SAAS;AAEvC,UAAI,OAAO,mBAAmB,YAAY;AACzC,eAAO;AAAA,MACR;AAEA,aAAO,gBAAgB,UAAU;AAAA,IAClC;AAAA,IAYO,gBACN,WACA,UACO;AACP,uBAAiB,QAAQ;AAEzB,YAAM,UAAU,KAAK,cAAc,WAAW,UAAU,KAAK;AAE7D,WAAK,aAAa,WAAW,SAAS,IAAI;AAE1C,aAAO;AAAA,IACR;AAAA,IAYO,oBACN,WACA,UACO;AACP,uBAAiB,QAAQ;AAEzB,YAAM,UAAU,KAAK,cAAc,WAAW,UAAU,IAAI;AAE5D,WAAK,aAAa,WAAW,SAAS,IAAI;AAE1C,aAAO;AAAA,IACR;AAAA,IAEO,aAAmG;AACzG,aAAO,KAAK,cAAc,IACvB,QAAQ,QAAQ,KAAK,OAAO,IAG5B,CAAC;AAAA,IACL;AAAA,IAEA,MAAa,gCAAgC;AAC5C,YAAM,WAAW,CAAC,GAAG,KAAK,oBAAoB,OAAO,CAAC;AAEtD,UAAI,SAAS,WAAW,GAAG;AAC1B,eAAO;AAAA,MACR;AAEA,YAAM,QAAQ,IAAI,QAAQ;AAE1B,aAAO;AAAA,IACR;AAAA,IAEQ,aAAa,WAAwB,iBAA2B,SAAkB;AAEzF,UAAI,KAAK,QAAQ,gBAAgB,QAAW;AAE3C,aAAK;AAAA,UACJ;AAAA,UACA;AAAA,UACE,gBAA6B,YAAY;AAAA,QAC5C;AAAA,MACD;AAEA,UAAI,WAAW,KAAK,QAAQ,SAAS;AAErC,UAAI,aAAa,QAAW;AAE3B,mBAAW,KAAK,QAAQ,SAAS,IAAI;AACrC,UAAE,KAAK;AAAA,MACR,WAAW,OAAO,aAAa,YAAY;AAG1C,mBAAW,KAAK,QAAQ,SAAS,IAAI,UAAU,CAAC,iBAAiB,QAAQ,IAAI,CAAC,UAAU,eAAe;AAAA,MAExG,WAAW,SAAS;AACnB,iBAAS,QAAQ,eAAe;AAAA,MACjC,OAAO;AACN,iBAAS,KAAK,eAAe;AAAA,MAC9B;AAEA,YAAM,kCAAkC,QAAQ,IAAI,UAAU,6BAA6B;AAE3F,UAAI,KAAK,gBAAgB,KAAK,SAAS,SAAS,KAAK,iBAAiB,CAAC,iCAAiC;AACvG,gBAAQ,IAAI,UAAU,+BAA+B,IAAI;AACzD,cAAM,iBAAiB;AAAA,UACtB,oDAAoD,SAAS,MAAM,IAAI;AAAA,YACtE;AAAA,UACD,CAAC,uBAAuB,KAAK,YAAY,IAAI;AAAA,UAC7C;AAAA,QACD,EAAE,KAAK,GAAG;AACV,gBAAQ,KAAK,cAAc;AAAA,MAC5B;AAAA,IACD;AAAA,IAEQ,cAAc,WAAwB,UAAoC,MAAyB;AAC1G,UAAI,CAAC,MAAM;AACV,eAAO;AAAA,MACR;AAEA,YAAM,QAAQ;AAAA,QACb,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,cAAc;AAAA,QACd;AAAA,QACA;AAAA,MACD;AAEA,YAAM,UAAU;AAEhB,YAAM,UAAU,QAAQ,KAAK,KAAK;AAClC,cAAQ,WAAW;AACnB,YAAM,SAAS;AAEf,aAAO;AAAA,IACR;AAAA,IASA,OAAc,cAAc,SAAiC,WAA4B;AACxF,aAAO,QAAQ,cAAc,SAAS;AAAA,IACvC;AAAA,IAiBA,aAAoB,KACnB,SACA,WACA,UAA4B,CAAC,GAC5B;AACD,YAAM,SAAS,SAAS;AACxB,0BAAoB,MAAM;AAE1B,UAAI,QAAQ,SAAS;AACpB,cAAM,IAAI,WAAW,QAAW,EAAE,OAAO,UAAU,MAAM,EAAE,CAAC;AAAA,MAC7D;AAEA,aAAO,IAAI,QAAe,CAAC,SAAS,WAAW;AAC9C,cAAM,gBAAgB,wBAAC,QAAiB;AACvC,kBAAQ,eAAe,WAAW,QAAQ;AAE1C,cAAI,QAAQ;AACX,8CAAkC,SAAS,WAAW,aAAa;AAAA,UACpE;AAEA,iBAAO,GAAG;AAAA,QACX,GARsB;AAUtB,cAAM,WAAW,2BAAI,SAAgB;AACpC,kBAAQ,eAAe,SAAS,aAAa;AAE7C,cAAI,QAAQ;AACX,8CAAkC,QAAQ,SAAS,aAAa;AAAA,UACjE;AAEA,kBAAQ,IAAa;AAAA,QACtB,GARiB;AAUjB,gBAAQ,KAAK,WAAW,QAAQ;AAChC,YAAI,cAAc,SAAS;AAC1B,kBAAQ,KAAK,SAAS,aAAa;AAAA,QACpC;AAEA,cAAM,gBAAgB,6BAAM;AAC3B,4CAAkC,SAAS,WAAW,QAAQ;AAC9D,4CAAkC,SAAS,SAAS,aAAa;AACjE,iBAAO,IAAI,WAAW,QAAW,EAAE,OAAO,UAAU,MAAM,EAAE,CAAC,CAAC;AAAA,QAC/D,GAJsB;AAMtB,YAAI,QAAQ;AACX,yCAA+B,QAAQ,SAAS,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,QAC9E;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAiBA,OAAc,GACb,SACA,WACA,UAA4B,CAAC,GACC;AAC9B,YAAM,SAAS,SAAS;AACxB,0BAAoB,MAAM;AAE1B,UAAI,QAAQ,SAAS;AACpB,cAAM,IAAI,WAAW,QAAW,EAAE,OAAO,UAAU,MAAM,EAAE,CAAC;AAAA,MAC7D;AAEA,YAAM,mBAAgC,CAAC;AACvC,YAAM,qBAAmG,CAAC;AAC1G,UAAI,QAAiB;AACrB,UAAI,WAAW;AAEf,YAAM,gBAAgB,6BAAM;AAC3B,qBAAa,IAAI,WAAW,QAAW,EAAE,OAAO,UAAU,MAAM,EAAE,CAAC,CAAC;AAAA,MACrE,GAFsB;AAItB,YAAM,eAAe,2BAAI,SAAoB;AAC5C,cAAM,UAAU,mBAAmB,MAAM;AACzC,YAAI,SAAS;AACZ,kBAAQ,QAAQ,iBAAiB,MAAM,KAAK,CAAC;AAAA,QAC9C,OAAO;AACN,2BAAiB,KAAK,IAAI;AAAA,QAC3B;AAAA,MACD,GAPqB;AASrB,YAAM,eAAe,wBAAC,QAAiB;AACtC,mBAAW;AAEX,cAAM,UAAU,mBAAmB,MAAM;AAEzC,YAAI,SAAS;AACZ,kBAAQ,OAAO,GAAG;AAAA,QACnB,OAAO;AACN,kBAAQ;AAAA,QACT;AAEA,aAAK,SAAS,OAAO;AAAA,MACtB,GAZqB;AAcrB,YAAM,WAAwC,OAAO;AAAA,QACpD;AAAA,UACC,OAAO;AAEN,kBAAM,QAAQ,iBAAiB,MAAM;AACrC,gBAAI,OAAO;AACV,qBAAO,QAAQ,QAAQ,iBAAiB,OAAO,KAAK,CAAC;AAAA,YACtD;AAKA,gBAAI,OAAO;AACV,oBAAM,IAAI,QAAQ,OAAO,KAAK;AAE9B,sBAAQ;AACR,qBAAO;AAAA,YACR;AAGA,gBAAI,UAAU;AACb,qBAAO,QAAQ,QAAQ,iBAAiB,QAAW,IAAI,CAAC;AAAA,YACzD;AAGA,mBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,iCAAmB,KAAK,EAAE,SAAS,OAAO,CAAC;AAAA,YAC5C,CAAC;AAAA,UACF;AAAA,UAEA,SAAS;AACR,oBAAQ,IAAI,WAAW,YAAY;AACnC,oBAAQ,IAAI,SAAS,YAAY;AAEjC,gBAAI,QAAQ;AACX,gDAAkC,QAAQ,SAAS,aAAa;AAAA,YACjE;AAEA,uBAAW;AAEX,kBAAM,aAAa,iBAAiB,QAAW,IAAI;AACnD,uBAAW,WAAW,oBAAoB;AACzC,sBAAQ,QAAQ,UAAU;AAAA,YAC3B;AAEA,mBAAO,QAAQ,QAAQ,UAAU;AAAA,UAClC;AAAA,UAEA,MAAM,KAAc;AACnB,gBAAI,CAAC,OAAO,EAAE,eAAe,QAAQ;AACpC,oBAAM,IAAI;AAAA,gBACT,gFAAgF,GAAG;AAAA,cACpF;AAAA,YACD;AAEA,oBAAQ;AACR,oBAAQ,IAAI,WAAW,YAAY;AACnC,oBAAQ,IAAI,SAAS,YAAY;AAAA,UAClC;AAAA,UAEA,CAAC,OAAO,aAAa,IAAI;AACxB,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,QACA;AAAA,MACD;AAEA,cAAQ,GAAG,WAAW,YAAY;AAClC,UAAI,cAAc,SAAS;AAC1B,gBAAQ,GAAG,SAAS,YAAY;AAAA,MACjC;AAEA,UAAI,QAAQ;AACX,uCAA+B,QAAQ,SAAS,aAAa;AAAA,MAC9D;AAEA,aAAO;AAAA,IACR;AAAA,EACD;AAtvBuD;AAAhD,MAAM,oBAAN;AAgxBP,WAAS,cAAsE;AAC9E,QAAI,CAAC,KAAK,OAAO;AAChB,WAAK,aAAa,eAAe,KAAK,WAAW,KAAK,MAAM;AAC5D,WAAK,QAAQ;AAEb,UAAI,UAAU,WAAW,GAAG;AAE3B,eAAO,KAAK,SAAS,KAAK,KAAK,YAAY;AAAA,MAC5C;AAGA,aAAO,KAAK,SAAS,MAAM,KAAK,cAAc,SAA4B;AAAA,IAC3E;AAAA,EACD;AAbS;AAmBT,WAAS,UAAU,QAAa;AAC/B,WAAO,QAAQ;AAAA,EAChB;AAFS;AAIT,WAAS,kCACR,SACA,MACA,UACA,OACC;AACD,QAAI,OAAO,QAAQ,QAAQ,YAAY;AACtC,cAAQ,IAAI,MAAM,QAAQ;AAAA,IAC3B,WAAW,OAAO,QAAQ,wBAAwB,YAAY;AAC7D,cAAQ,oBAAoB,MAAM,UAAU,KAAK;AAAA,IAClD;AAAA,EACD;AAXS;AAaT,WAAS,+BACR,SACA,MACA,UACA,OACC;AACD,QAAI,OAAO,QAAQ,OAAO,YAAY;AACrC,UAAI,OAAO,MAAM;AAChB,gBAAQ,KAAM,MAAM,QAAQ;AAAA,MAC7B,OAAO;AACN,gBAAQ,GAAG,MAAM,QAAQ;AAAA,MAC1B;AAAA,IACD,WAAW,OAAO,QAAQ,qBAAqB,YAAY;AAC1D,cAAQ,iBAAiB,MAAM,UAAU,KAAK;AAAA,IAC/C;AAAA,EACD;AAfS;AAsBT,MAAM,yBAAyB,OAAO,eAAe,OAAO,eAAe,mBAAmB;AAAA,EAAC,CAAC,EAAE,SAAS;AAE3G,WAAS,iBAAiB,OAAgB,MAAe;AACxD,WAAO,EAAE,OAAO,KAAK;AAAA,EACtB;AAFS;AAQF,MAAM,cAAN,MAAM,oBAAmB,MAAM;AAAA,IAI9B,YAAY,UAAU,6BAA6B,UAAyC,QAAW;AAC7G,UAAI,YAAY,UAAa,OAAO,YAAY,UAAU;AACzD,cAAM,IAAI,UAAU,oEAAoE;AAAA,MACzF;AAEA,YAAM,SAAS,OAAO;AARvB,WAAgB,OAAO;AACvB,WAAyB,OAAO;AAAA,IAQhC;AAAA,EACD;AAXsC;AAA/B,MAAM,aAAN;AAaP,WAAS,iBAAiB,SAAiC,QAAa;AACvE,QAAI;AACH,YAAM,MAAM,OAAO;AACnB,YAAM,MAAM,OAAO;AAEnB,UAAI,OAAO,QAAQ,YAAY;AAC9B,YAAI,KAAK,QAAQ,QAAW,CAAC,UAAe;AAE3C,qBAAW,MAAM;AAChB,oBAAQ,KAAK,SAAS,KAAK;AAAA,UAC5B,GAAG,CAAC;AAAA,QACL,CAAC;AAAA,MACF;AAEA,UAAI,OAAO,QAAQ,YAAY;AAC9B,cAAM,YAAY,OAAO,EAAE,QAAQ,YAAY,CAAC;AAChD,gBAAQ,qBAAqB,EAAE,IAAI,WAAW,MAAM;AACpD,YAAI,KAAK,QAAQ,gCAAS,QAAQ;AACjC,kBAAQ,qBAAqB,EAAE,OAAO,SAAS;AAAA,QAChD,GAFiB,QAEhB;AAAA,MACF;AAAA,IACD,SAAS,KAAK;AACb,cAAQ,KAAK,SAAS,GAAG;AAAA,IAC1B;AAAA,EACD;AAxBS;", "names": []}