{"version": 3, "file": "oauth2.d.ts", "sourceRoot": "", "sources": ["oauth2.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAE5G;;GAEG;AACH,MAAM,MAAM,wCAAwC,GAAG,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;AAErF;;GAEG;AACH,MAAM,WAAW,0CAA0C;IAC1D;;OAEG;IACH,WAAW,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IACrC;;OAEG;IACH,MAAM,EAAE,YAAY,EAAE,CAAC;IACvB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,IAAI,CAAC,EAAE,OAAO,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,4BAA4B;IAC5C,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,SAAS,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;IACd,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;CAC5B;AAED;;GAEG;AACH,MAAM,WAAW,kCAAkC;IAClD,KAAK,EAAE,MAAM,CAAC;IACd,eAAe,CAAC,EAAE,cAAc,GAAG,eAAe,CAAC;CACnD;AAED;;GAEG;AACH,MAAM,WAAW,sCAAsC;IACtD,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,kCAAkC,GAAG,sCAAsC,CAAC;AAExF;;;;;GAKG;AACH,MAAM,MAAM,uCAAuC,GAAG,wCAAwC,GAAG;IAChG,UAAU,EAAE,oBAAoB,CAAC;IACjC,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,CAAC,EAAE,MAAM,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,wCAAwC,GACjD;IAAE,SAAS,EAAE,SAAS,CAAC;IAAC,aAAa,EAAE,MAAM,CAAA;CAAE,GAC/C;IAAE,SAAS,CAAC,EAAE,KAAK,CAAC;IAAC,aAAa,CAAC,EAAE,KAAK,CAAA;CAAE,CAAC;AAEhD;;GAEG;AACH,MAAM,WAAW,+BAA+B;IAC/C,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,KAAK,EAAE,MAAM,CAAC;CACd;AAED;;;;;GAKG;AACH,MAAM,MAAM,wCAAwC,GAAG,wCAAwC,GAAG;IACjG,UAAU,EAAE,eAAe,CAAC;IAC5B,aAAa,EAAE,MAAM,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,gCAAgC,GAAG,+BAA+B,CAAC;AAE/E;;GAEG;AACH,MAAM,WAAW,oCAAoC;IACpD,aAAa,EAAE,OAAO,CAAC;IACvB,SAAS,EAAE,SAAS,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;IACd,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;CAC5B;AAED;;GAEG;AACH,MAAM,MAAM,gDAAgD,GAAG,IAAI,CAAC,+BAA+B,EAAE,eAAe,CAAC,CAAC;AAEtH;;GAEG;AACH,MAAM,WAAW,6CAA6C;IAC7D,UAAU,EAAE,oBAAoB,CAAC;IACjC,KAAK,EAAE,MAAM,CAAC;CACd;AAED,MAAM,MAAM,qCAAqC,GAAG,gDAAgD,CAAC;AAErG;;GAEG;AACH,MAAM,WAAW,+BAA+B;IAC/C;;OAEG;IACH,SAAS,EAAE,SAAS,CAAC;IACrB;;OAEG;IACH,KAAK,EACF,GAAG,YAAY,CAAC,GAAG,IAAI,MAAM,EAAE,GAC/B,GAAG,YAAY,CAAC,GAAG,EAAE,GACrB,GAAG,MAAM,IAAI,YAAY,CAAC,GAAG,IAAI,MAAM,EAAE,GACzC,GAAG,MAAM,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;IACnC;;;;OAIG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B;;OAEG;IACH,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB;;OAEG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;CAC/B;AAED;;GAEG;AACH,MAAM,WAAW,uCAAuC;IACvD,SAAS,EAAE,SAAS,CAAC;IACrB;;OAEG;IACH,KAAK,EACF,GAAG,YAAY,CAAC,GAAG,IAAI,MAAM,EAAE,GAC/B,GAAG,YAAY,CAAC,GAAG,EAAE,GACrB,GAAG,MAAM,IAAI,YAAY,CAAC,GAAG,IAAI,MAAM,EAAE,GACzC,GAAG,MAAM,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;IACnC;;OAEG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,6CAA6C;IAC7D,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,SAAS,CAAC;IACpB,WAAW,EAAE,WAAW,CAAC;CACzB;AAED;;GAEG;AACH,MAAM,WAAW,oDAAoD;IACpE,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,QAAQ,CAAC;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,6DAA6D;IAC7E,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,UAAU,CAAC;CACpB;AAED,MAAM,MAAM,sEAAsE,GACjF,oDAAoD,GACnD,6DAA6D,CAAC"}